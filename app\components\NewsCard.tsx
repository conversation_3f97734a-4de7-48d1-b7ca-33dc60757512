import { Link } from "react-router";
import { Calendar, MapPin, ExternalLink, Eye, Tag } from "lucide-react";
import type { NewsArticle } from "~/types";

interface NewsCardProps {
  article: NewsArticle;
  showActions?: boolean;
  onEdit?: (id: string) => void;
  onDelete?: (id: string) => void;
}

export function NewsCard({ article, showActions = false, onEdit, onDelete }: NewsCardProps) {
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('ar-SA', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    }).format(date);
  };

  const truncateText = (text: string, maxLength: number) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  const getCategoryColor = (categoryName: string) => {
    const colors = {
      'انتهاكات حقوق الإنسان': 'bg-red-100 text-red-800',
      'العنف ضد المرأة': 'bg-pink-100 text-pink-800',
      'حقوق الطفل': 'bg-blue-100 text-blue-800',
      'الحريات العامة': 'bg-green-100 text-green-800',
      'العدالة': 'bg-purple-100 text-purple-800',
      'التعذيب': 'bg-orange-100 text-orange-800',
    };
    return colors[categoryName as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="bg-white rounded-lg shadow-md hover-card overflow-hidden">
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-2">
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getCategoryColor(article.category?.name || '')}`}>
            <Tag className="h-3 w-3 ml-1" />
            {article.category?.name}
          </span>
          <div className="flex items-center text-sm text-gray-500 space-x-1 space-x-reverse">
            <Calendar className="h-4 w-4" />
            <span>{formatDate(article.publishedDate)}</span>
          </div>
        </div>
        
        <h3 className="text-lg font-semibold text-gray-900 mb-2 leading-tight">
          <Link 
            to={`/news/${article.id}`}
            className="hover:text-blue-600 transition-colors"
          >
            {truncateText(article.title, 80)}
          </Link>
        </h3>
        
        <div className="flex items-center text-sm text-gray-600 space-x-4 space-x-reverse">
          <div className="flex items-center space-x-1 space-x-reverse">
            <MapPin className="h-4 w-4" />
            <span>{article.province?.name}</span>
          </div>
          <div className="flex items-center space-x-1 space-x-reverse">
            <ExternalLink className="h-4 w-4" />
            <span>{article.source?.name}</span>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-4">
        <p className="text-gray-700 text-sm leading-relaxed mb-4">
          {truncateText(article.content, 150)}
        </p>

        {/* Tags */}
        {article.tags && article.tags.length > 0 && (
          <div className="flex flex-wrap gap-1 mb-4">
            {article.tags.slice(0, 3).map((tag, index) => (
              <span 
                key={index}
                className="inline-block bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded"
              >
                #{tag}
              </span>
            ))}
            {article.tags.length > 3 && (
              <span className="text-xs text-gray-500">
                +{article.tags.length - 3} المزيد
              </span>
            )}
          </div>
        )}

        {/* Dynamic Fields Preview */}
        {article.dynamicData && Object.keys(article.dynamicData).length > 0 && (
          <div className="bg-gray-50 rounded-md p-3 mb-4">
            <h4 className="text-sm font-medium text-gray-700 mb-2">معلومات إضافية:</h4>
            <div className="space-y-1">
              {Object.entries(article.dynamicData).slice(0, 2).map(([key, value]) => (
                <div key={key} className="flex justify-between text-xs">
                  <span className="text-gray-600">{key}:</span>
                  <span className="text-gray-800 font-medium">
                    {typeof value === 'string' ? truncateText(value, 30) : String(value)}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="px-4 py-3 bg-gray-50 border-t border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2 space-x-reverse text-sm text-gray-500">
            <Eye className="h-4 w-4" />
            <span>{article.viewCount || 0} مشاهدة</span>
          </div>
          
          <div className="flex items-center space-x-2 space-x-reverse">
            {showActions && (
              <>
                <button
                  onClick={() => onEdit?.(article.id)}
                  className="text-blue-600 hover:text-blue-800 text-sm font-medium transition-colors"
                >
                  تعديل
                </button>
                <button
                  onClick={() => onDelete?.(article.id)}
                  className="text-red-600 hover:text-red-800 text-sm font-medium transition-colors"
                >
                  حذف
                </button>
              </>
            )}
            
            <Link
              to={`/news/${article.id}`}
              className="inline-flex items-center px-3 py-1 border border-blue-300 text-sm font-medium rounded-md text-blue-700 bg-white hover:bg-blue-50 transition-colors"
            >
              <Eye className="h-4 w-4 ml-1" />
              عرض التفاصيل
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
