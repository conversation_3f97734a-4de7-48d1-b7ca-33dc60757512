import { useState, useEffect } from "react";
import { useNavigate } from "react-router";
import { ArrowRight, Save, AlertCircle } from "lucide-react";

import {
  MockNewsService as NewsService,
  MockClassificationService as ClassificationService,
  MockViolationService as ViolationService,
  MockSourceService as SourceService,
  MockProvinceService as ProvinceService
} from "~/lib/mockData";
import type { Classification, Source, Violation, Province, NewsArticle } from "~/types";

export default function AddNews() {
  const navigate = useNavigate();
  const [classifications, setClassifications] = useState<Classification[]>([]);
  const [violations, setViolations] = useState<Violation[]>([]);
  const [sources, setSources] = useState<Source[]>([]);
  const [provinces, setProvinces] = useState<Province[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const [formData, setFormData] = useState({
    title: '',
    content: '',
    provinceId: '',
    date: new Date().toISOString().split('T')[0],
    sourceUrl: '',
    sourceId: '',
    classificationId: '',
    violationId: ''
  });

  useEffect(() => {
    loadData();
  }, []);

  useEffect(() => {
    if (formData.classificationId) {
      loadViolations();
    } else {
      setViolations([]);
      setFormData(prev => ({ ...prev, violationId: '' }));
    }
  }, [formData.classificationId]);

  const loadData = async () => {
    try {
      setLoading(true);
      const [classificationsData, sourcesData, provincesData] = await Promise.all([
        ClassificationService.getAll(),
        SourceService.getAll(),
        ProvinceService.getAll()
      ]);
      setClassifications(classificationsData);
      setSources(sourcesData);
      setProvinces(provincesData);
    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadViolations = async () => {
    try {
      const violationsData = await ViolationService.getByClassificationId(formData.classificationId);
      setViolations(violationsData);
      setFormData(prev => ({ ...prev, violationId: '' }));
    } catch (error) {
      console.error('Error loading violations:', error);
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = 'عنوان الخبر مطلوب';
    }

    if (!formData.content.trim()) {
      newErrors.content = 'محتوى الخبر مطلوب';
    }

    if (!formData.provinceId) {
      newErrors.provinceId = 'المحافظة مطلوبة';
    }

    if (!formData.date) {
      newErrors.date = 'تاريخ الخبر مطلوب';
    }

    if (!formData.sourceUrl.trim()) {
      newErrors.sourceUrl = 'رابط المصدر مطلوب';
    }

    if (!formData.classificationId) {
      newErrors.classificationId = 'التصنيف مطلوب';
    }

    if (!formData.violationId) {
      newErrors.violationId = 'الانتهاك مطلوب';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      setSaving(true);

      const newsData: Omit<NewsArticle, 'id'> = {
        title: formData.title,
        content: formData.content,
        provinceId: formData.provinceId,
        date: formData.date,
        sourceUrl: formData.sourceUrl,
        sourceId: formData.sourceId || undefined,
        classificationId: formData.classificationId,
        violationId: formData.violationId,
        createdBy: 'user1' // This should come from auth context
      };

      await NewsService.create(newsData);
      navigate('/news/manage');
    } catch (error) {
      console.error('Error saving news:', error);
      setErrors({ submit: 'حدث خطأ أثناء حفظ الخبر' });
    } finally {
      setSaving(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">جاري تحميل البيانات...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={() => navigate('/news/manage')}
            className="flex items-center text-blue-600 hover:text-blue-800 mb-4"
          >
            <ArrowRight className="w-5 h-5 ml-2" />
            العودة إلى إدارة الأخبار
          </button>
          <h1 className="text-3xl font-bold text-gray-900">إضافة خبر جديد</h1>
          <p className="mt-2 text-gray-600">أضف خبراً جديداً حول انتهاكات حقوق الإنسان</p>
        </div>

        {/* Form */}
        <div className="bg-white shadow-lg rounded-lg">
          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            {/* Error Message */}
            {errors.submit && (
              <div className="bg-red-50 border border-red-200 rounded-md p-4">
                <div className="flex">
                  <AlertCircle className="h-5 w-5 text-red-400" />
                  <div className="mr-3">
                    <p className="text-sm text-red-800">{errors.submit}</p>
                  </div>
                </div>
              </div>
            )}

            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Title */}
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  عنوان الخبر *
                </label>
                <input
                  type="text"
                  value={formData.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.title ? 'border-red-300' : 'border-gray-300'
                  }`}
                  placeholder="أدخل عنوان الخبر"
                />
                {errors.title && <p className="mt-1 text-sm text-red-600">{errors.title}</p>}
              </div>

              {/* Province */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  المحافظة *
                </label>
                <select
                  value={formData.provinceId}
                  onChange={(e) => handleInputChange('provinceId', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.provinceId ? 'border-red-300' : 'border-gray-300'
                  }`}
                >
                  <option value="">اختر المحافظة</option>
                  {provinces.map(province => (
                    <option key={province.id} value={province.id}>
                      {province.name}
                    </option>
                  ))}
                </select>
                {errors.provinceId && <p className="mt-1 text-sm text-red-600">{errors.provinceId}</p>}
              </div>

              {/* Date */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  تاريخ الخبر *
                </label>
                <input
                  type="date"
                  value={formData.date}
                  onChange={(e) => handleInputChange('date', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.date ? 'border-red-300' : 'border-gray-300'
                  }`}
                />
                {errors.date && <p className="mt-1 text-sm text-red-600">{errors.date}</p>}
              </div>

              {/* Source URL */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  رابط المصدر *
                </label>
                <input
                  type="url"
                  value={formData.sourceUrl}
                  onChange={(e) => handleInputChange('sourceUrl', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.sourceUrl ? 'border-red-300' : 'border-gray-300'
                  }`}
                  placeholder="https://example.com/news"
                />
                {errors.sourceUrl && <p className="mt-1 text-sm text-red-600">{errors.sourceUrl}</p>}
              </div>

              {/* Source (Optional) */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  المصدر (اختياري)
                </label>
                <select
                  value={formData.sourceId}
                  onChange={(e) => handleInputChange('sourceId', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">اختر المصدر</option>
                  {sources.map(source => (
                    <option key={source.id} value={source.id}>
                      {source.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Content */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                محتوى الخبر *
              </label>
              <textarea
                value={formData.content}
                onChange={(e) => handleInputChange('content', e.target.value)}
                rows={6}
                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors.content ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="أدخل تفاصيل الخبر"
              />
              {errors.content && <p className="mt-1 text-sm text-red-600">{errors.content}</p>}
            </div>

            {/* Classification and Violation */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Classification */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  التصنيف *
                </label>
                <select
                  value={formData.classificationId}
                  onChange={(e) => handleInputChange('classificationId', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.classificationId ? 'border-red-300' : 'border-gray-300'
                  }`}
                >
                  <option value="">اختر التصنيف</option>
                  {classifications.map(classification => (
                    <option key={classification.id} value={classification.id}>
                      {classification.name}
                    </option>
                  ))}
                </select>
                {errors.classificationId && <p className="mt-1 text-sm text-red-600">{errors.classificationId}</p>}
              </div>

              {/* Violation */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  الانتهاك *
                </label>
                <select
                  value={formData.violationId}
                  onChange={(e) => handleInputChange('violationId', e.target.value)}
                  disabled={!formData.classificationId}
                  className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.violationId ? 'border-red-300' : 'border-gray-300'
                  } ${!formData.classificationId ? 'bg-gray-100' : ''}`}
                >
                  <option value="">اختر الانتهاك</option>
                  {violations.map(violation => (
                    <option key={violation.id} value={violation.id}>
                      {violation.name}
                    </option>
                  ))}
                </select>
                {errors.violationId && <p className="mt-1 text-sm text-red-600">{errors.violationId}</p>}
                {!formData.classificationId && (
                  <p className="mt-1 text-sm text-gray-500">اختر التصنيف أولاً</p>
                )}
              </div>
            </div>

            {/* Submit Button */}
            <div className="flex justify-end pt-6 border-t border-gray-200">
              <button
                type="submit"
                disabled={saving}
                className="flex items-center px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {saving ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                    جاري الحفظ...
                  </>
                ) : (
                  <>
                    <Save className="w-4 h-4 ml-2" />
                    حفظ الخبر
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
