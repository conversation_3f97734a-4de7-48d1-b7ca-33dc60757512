import { useState, useEffect } from "react";
import { Link } from "react-router";
import { Plus, Edit, Trash2, Eye, FileText, Users } from "lucide-react";
import { Layout } from "~/components/Layout";
import type { MainCategory, Violation } from "~/types";
import { 
  MockMainCategoryService as MainCategoryService,
  MockViolationService as ViolationService,
  MockNewsService as NewsService
} from "~/lib/mockData";

function MainCategoriesPage() {
  const [categories, setCategories] = useState<MainCategory[]>([]);
  const [violations, setViolations] = useState<Violation[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      console.log('Loading main categories data...');
      const [categoriesData, violationsData] = await Promise.all([
        MainCategoryService.getAll(),
        ViolationService.getAll()
      ]);
      console.log('Categories loaded:', categoriesData);
      console.log('Violations loaded:', violationsData);
      setCategories(categoriesData);
      setViolations(violationsData);
    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getViolationCount = (categoryId: string) => {
    return violations.filter(v => v.mainCategoryId === categoryId).length;
  };

  const getNewsCount = async (categoryId: string) => {
    try {
      const newsData = await NewsService.getAll();
      return newsData.data.filter(n => n.mainCategoryId === categoryId).length;
    } catch (error) {
      return 0;
    }
  };

  const filteredCategories = categories.filter(category =>
    category.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    category.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleDelete = async (id: string) => {
    if (window.confirm('هل أنت متأكد من حذف هذا التصنيف؟')) {
      try {
        await MainCategoryService.delete(id);
        setCategories(categories.filter(c => c.id !== id));
      } catch (error) {
        console.error('Error deleting category:', error);
        alert('حدث خطأ أثناء حذف التصنيف');
      }
    }
  };

  if (loading) {
    return (
      <Layout>
        <div className="flex justify-center items-center min-h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحميل التصنيفات...</p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">إدارة التصنيفات الرئيسية</h1>
            <p className="text-gray-600 mt-2">
              إدارة الحقوق الأساسية التي صادق عليها العراق
            </p>
          </div>
          <Link
            to="/main-categories/add"
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium flex items-center gap-2 transition-colors"
          >
            <Plus className="h-5 w-5" />
            إضافة تصنيف جديد
          </Link>
        </div>

        {/* Search */}
        <div className="mb-6">
          <input
            type="text"
            placeholder="البحث في التصنيفات..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full max-w-md px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        {/* Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-blue-100">
                <FileText className="h-6 w-6 text-blue-600" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">إجمالي التصنيفات</p>
                <p className="text-2xl font-bold text-gray-900">{categories.length}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-green-100">
                <Users className="h-6 w-6 text-green-600" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">التصنيفات النشطة</p>
                <p className="text-2xl font-bold text-gray-900">
                  {categories.filter(c => c.isActive).length}
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-purple-100">
                <FileText className="h-6 w-6 text-purple-600" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">إجمالي الانتهاكات</p>
                <p className="text-2xl font-bold text-gray-900">{violations.length}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Categories Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredCategories.map((category) => (
            <div key={category.id} className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow border-r-4 border-r-blue-500">
              <div className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      {category.name}
                    </h3>
                    <p className="text-gray-600 text-sm mb-3">
                      {category.description}
                    </p>
                  </div>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    category.isActive 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {category.isActive ? 'نشط' : 'غير نشط'}
                  </span>
                </div>

                <div className="flex justify-between items-center text-sm text-gray-500 mb-4">
                  <span>{getViolationCount(category.id)} انتهاك</span>
                </div>

                <div className="flex gap-2">
                  <Link
                    to={`/main-categories/${category.id}`}
                    className="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 py-2 px-3 rounded text-center text-sm font-medium transition-colors flex items-center justify-center gap-1"
                  >
                    <Eye className="h-4 w-4" />
                    عرض
                  </Link>
                  <Link
                    to={`/main-categories/${category.id}/edit`}
                    className="flex-1 bg-blue-100 hover:bg-blue-200 text-blue-700 py-2 px-3 rounded text-center text-sm font-medium transition-colors flex items-center justify-center gap-1"
                  >
                    <Edit className="h-4 w-4" />
                    تعديل
                  </Link>
                  <button
                    onClick={() => handleDelete(category.id)}
                    className="flex-1 bg-red-100 hover:bg-red-200 text-red-700 py-2 px-3 rounded text-center text-sm font-medium transition-colors flex items-center justify-center gap-1"
                  >
                    <Trash2 className="h-4 w-4" />
                    حذف
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {filteredCategories.length === 0 && (
          <div className="text-center py-12">
            <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد تصنيفات</h3>
            <p className="text-gray-600 mb-4">
              {searchTerm ? 'لم يتم العثور على تصنيفات تطابق البحث' : 'لم يتم إنشاء أي تصنيفات بعد'}
            </p>
            {!searchTerm && (
              <Link
                to="/main-categories/add"
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium inline-flex items-center gap-2 transition-colors"
              >
                <Plus className="h-5 w-5" />
                إضافة تصنيف جديد
              </Link>
            )}
          </div>
        )}
      </div>
    </Layout>
  );
}

export default MainCategoriesPage;
