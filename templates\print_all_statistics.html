<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إحصائيات النظام - نظام الرصد الإعلامي</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap');
        
        body {
            font-family: 'Tajawal', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: white;
            color: #333;
            line-height: 1.6;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #007bff;
            padding-bottom: 20px;
        }
        
        .header img {
            max-height: 80px;
            margin-bottom: 10px;
        }
        
        .header h1 {
            color: #007bff;
            margin: 10px 0;
            font-size: 28px;
            font-weight: 700;
        }
        
        .header .date {
            color: #666;
            font-size: 14px;
            margin: 5px 0;
        }
        
        .stats-section {
            margin-bottom: 30px;
            page-break-inside: avoid;
        }
        
        .stats-section h2 {
            color: #007bff;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            margin-bottom: 20px;
            font-size: 20px;
            font-weight: 600;
        }
        
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .summary-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
        }
        
        .summary-card h3 {
            font-size: 32px;
            color: #007bff;
            margin: 0 0 10px 0;
            font-weight: 700;
        }
        
        .summary-card p {
            margin: 0;
            color: #666;
            font-weight: 500;
        }
        
        .stats-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            background: white;
        }
        
        .stats-table th,
        .stats-table td {
            border: 1px solid #dee2e6;
            padding: 12px;
            text-align: right;
        }
        
        .stats-table th {
            background: #007bff;
            color: white;
            font-weight: 600;
        }
        
        .stats-table tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .stats-table tr:hover {
            background: #e3f2fd;
        }
        
        .percentage {
            font-weight: 600;
            color: #007bff;
        }
        
        .footer {
            margin-top: 40px;
            text-align: center;
            color: #666;
            font-size: 12px;
            border-top: 1px solid #dee2e6;
            padding-top: 20px;
        }
        
        @media print {
            body {
                margin: 0;
                padding: 15px;
            }
            
            .stats-section {
                page-break-inside: avoid;
            }
            
            .summary-stats {
                grid-template-columns: repeat(4, 1fr);
            }
        }
    </style>
</head>
<body>
    <!-- رأس التقرير -->
    <div class="header">
        <img src="{{ url_for('static', filename='img/ihchr.png') }}" alt="شعار المؤسسة">
        <h1>تقرير إحصائيات النظام</h1>
        <div class="date">
            تاريخ التقرير: {{ moment().format('YYYY-MM-DD HH:mm') }}
            {% if date_filter_text %}
            <br>الفترة المحددة: {{ date_filter_text }}
            {% endif %}
        </div>
    </div>

    <!-- الإحصائيات العامة -->
    <div class="stats-section">
        <h2><i class="fas fa-chart-pie"></i> الإحصائيات العامة</h2>
        <div class="summary-stats">
            <div class="summary-card">
                <h3>{{ "{:,}".format(total_news) }}</h3>
                <p>إجمالي الأخبار</p>
            </div>
            <div class="summary-card">
                <h3>{{ total_categories }}</h3>
                <p>التصنيفات</p>
            </div>
            <div class="summary-card">
                <h3>{{ total_governorates }}</h3>
                <p>المحافظات</p>
            </div>
            <div class="summary-card">
                <h3>{{ total_sources }}</h3>
                <p>مصادر الأخبار</p>
            </div>
        </div>
    </div>

    <!-- إحصائيات التصنيفات -->
    {% if category_stats %}
    <div class="stats-section">
        <h2><i class="fas fa-tags"></i> الأخبار حسب التصنيف</h2>
        <table class="stats-table">
            <thead>
                <tr>
                    <th>الترتيب</th>
                    <th>التصنيف</th>
                    <th>عدد الأخبار</th>
                    <th>النسبة</th>
                </tr>
            </thead>
            <tbody>
                {% for stat in category_stats %}
                <tr>
                    <td>{{ loop.index }}</td>
                    <td>{{ stat.name }}</td>
                    <td>{{ stat.news_count }}</td>
                    <td class="percentage">{{ (stat.news_count / total_news * 100)|round(0)|int }}%</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% endif %}

    <!-- إحصائيات المحافظات -->
    {% if governorate_stats %}
    <div class="stats-section">
        <h2><i class="fas fa-map-marker-alt"></i> الأخبار حسب المحافظة</h2>
        <table class="stats-table">
            <thead>
                <tr>
                    <th>الترتيب</th>
                    <th>المحافظة</th>
                    <th>عدد الأخبار</th>
                    <th>النسبة</th>
                </tr>
            </thead>
            <tbody>
                {% for stat in governorate_stats %}
                <tr>
                    <td>{{ loop.index }}</td>
                    <td>{{ stat.name }}</td>
                    <td>{{ stat.news_count }}</td>
                    <td class="percentage">{{ (stat.news_count / total_news * 100)|round(0)|int }}%</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% endif %}

    <!-- إحصائيات المصادر -->
    {% if source_stats %}
    <div class="stats-section">
        <h2><i class="fas fa-rss"></i> الأخبار حسب المصدر</h2>
        <table class="stats-table">
            <thead>
                <tr>
                    <th>الترتيب</th>
                    <th>المصدر</th>
                    <th>عدد الأخبار</th>
                    <th>النسبة</th>
                </tr>
            </thead>
            <tbody>
                {% for stat in source_stats %}
                <tr>
                    <td>{{ loop.index }}</td>
                    <td>{{ stat.source }}</td>
                    <td>{{ stat.news_count }}</td>
                    <td class="percentage">{{ (stat.news_count / total_news * 100)|round(0)|int }}%</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% endif %}

    <!-- تذييل التقرير -->
    <div class="footer">
        <p>تم إنشاء هذا التقرير بواسطة نظام الرصد الإعلامي</p>
        <p>{{ moment().format('YYYY-MM-DD HH:mm:ss') }}</p>
    </div>

    <script>
        // طباعة تلقائية عند تحميل الصفحة
        window.onload = function() {
            window.print();
        };
    </script>
</body>
</html>
