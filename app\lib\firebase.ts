import { initializeApp } from 'firebase/app';
import { getAuth, connectAuthEmulator } from 'firebase/auth';
import { getFirestore, connectFirestoreEmulator } from 'firebase/firestore';
import { getStorage, connectStorageEmulator } from 'firebase/storage';

// Firebase configuration
const firebaseConfig = {
  apiKey: process.env.FIREBASE_API_KEY || "demo-api-key",
  authDomain: process.env.FIREBASE_AUTH_DOMAIN || "media-monitoring-demo.firebaseapp.com",
  projectId: process.env.FIREBASE_PROJECT_ID || "media-monitoring-demo",
  storageBucket: process.env.FIREBASE_STORAGE_BUCKET || "media-monitoring-demo.appspot.com",
  messagingSenderId: process.env.FIREBASE_MESSAGING_SENDER_ID || "123456789",
  appId: process.env.FIREBASE_APP_ID || "1:123456789:web:abcdef123456"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase services
export const auth = getAuth(app);
export const db = getFirestore(app);
export const storage = getStorage(app);

// Connect to emulators in development (only if explicitly enabled)
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development' && process.env.USE_FIREBASE_EMULATOR === 'true') {
  // Only run in browser and development mode when emulator is explicitly enabled
  try {
    // Connect to Auth emulator
    if (!(auth as any)._config?.emulator) {
      connectAuthEmulator(auth, 'http://localhost:9099');
    }

    // Connect to Firestore emulator
    if (!(db as any)._settings?.host?.includes('localhost')) {
      connectFirestoreEmulator(db, 'localhost', 8080);
    }

    // Connect to Storage emulator
    if (!(storage as any)._host?.includes('localhost')) {
      connectStorageEmulator(storage, 'localhost', 9199);
    }

    console.log('Connected to Firebase emulators');
  } catch (error) {
    console.log('Firebase emulators already connected or not available');
  }
} else if (typeof window !== 'undefined') {
  console.log('Using Firebase production services');
}

export default app;
