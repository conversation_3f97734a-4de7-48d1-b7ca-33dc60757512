{% extends "layout.html" %}

{% block content %}
<div class="container py-4">
    <!-- شريط التنقل -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('index') }}">الرئيسية</a></li>
            <li class="breadcrumb-item active" aria-current="page">إدارة مصادر الأخبار</li>
        </ol>
    </nav>

    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0"><i class="fas fa-rss me-2"></i>إدارة مصادر الأخبار</h2>
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addSourceModal">
            <i class="fas fa-plus-circle me-1"></i> إضافة مصدر جديد
        </button>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>معلومات</h5>
                </div>
                <div class="card-body">
                    <p>يمكنك إدارة مصادر الأخبار التي تستخدمها في النظام. أضف المصادر الموثوقة للرجوع إليها عند إضافة الأخبار يدوياً.</p>
                    <div class="alert alert-info">
                        <i class="fas fa-lightbulb me-2"></i> نصائح:
                        <ul class="mb-0 mt-2">
                            <li>أضف المصادر الإخبارية العراقية الرسمية والموثوقة.</li>
                            <li>تأكد من إضافة الرابط الصحيح للمصدر.</li>
                            <li>يمكنك تفعيل أو تعطيل المصادر حسب الحاجة.</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">قائمة المصادر</h5>
                    </div>
                </div>
                <div class="card-body">
                    {% if sources %}
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>الاسم</th>
                                        <th>الرابط</th>
                                        <th>النوع</th>
                                        <th>الحالة</th>
                                        <th>آخر جلب</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for source in sources %}
                                        <tr>
                                            <td>{{ loop.index }}</td>
                                            <td>{{ source.name }}</td>
                                            <td>
                                                <a href="{{ source.url }}" target="_blank" class="text-truncate d-inline-block" style="max-width: 200px;">
                                                    {{ source.url }}
                                                    <i class="fas fa-external-link-alt ms-1 small"></i>
                                                </a>
                                            </td>
                                            <td>
                                                {% if source.source_type == 'website' %}
                                                    <span class="badge bg-primary">موقع ويب</span>
                                                {% elif source.source_type == 'rss' %}
                                                    <span class="badge bg-success">RSS</span>
                                                {% elif source.source_type == 'api' %}
                                                    <span class="badge bg-info">API</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if source.is_active %}
                                                    <span class="badge bg-success">نشط</span>
                                                {% else %}
                                                    <span class="badge bg-danger">غير نشط</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if source.last_fetch %}
                                                    {{ source.last_fetch|format_date_arabic }}
                                                {% else %}
                                                    <span class="text-muted">لم يتم الجلب بعد</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <div class="btn-group">
                                                    <button class="btn btn-sm btn-primary edit-source-btn"
                                                            data-bs-toggle="modal"
                                                            data-bs-target="#editSourceModal"
                                                            data-id="{{ source.id }}"
                                                            data-name="{{ source.name }}"
                                                            data-url="{{ source.url }}"
                                                            data-type="{{ source.source_type }}"
                                                            data-active="{{ source.is_active }}"
                                                            data-selector-title="{{ source.selector_title or '' }}"
                                                            data-selector-content="{{ source.selector_content or '' }}"
                                                            data-selector-date="{{ source.selector_date or '' }}"
                                                            data-date-format="{{ source.date_format or '' }}"
                                                            data-keywords="{{ source.get_keywords()|tojson }}">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-danger delete-source-btn"
                                                            data-bs-toggle="modal"
                                                            data-bs-target="#deleteSourceModal"
                                                            data-id="{{ source.id }}"
                                                            data-name="{{ source.name }}">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i> لا توجد مصادر أخبار مسجلة حالياً.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نافذة إضافة مصدر جديد -->
<div class="modal fade" id="addSourceModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">إضافة مصدر جديد</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ url_for('add_source') }}" method="POST">
                <div class="modal-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="name" class="form-label">اسم المصدر <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                        <div class="col-md-6">
                            <label for="url" class="form-label">رابط المصدر <span class="text-danger">*</span></label>
                            <input type="url" class="form-control" id="url" name="url" required>
                            <small class="form-text text-muted">أدخل رابط الصفحة الرئيسية للموقع الإخباري</small>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="source_type" class="form-label">نوع المصدر</label>
                            <select class="form-select" id="source_type" name="source_type">
                                <option value="website">موقع ويب</option>
                                <option value="rss">RSS</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="is_active" class="form-label">الحالة</label>
                            <div class="form-check form-switch mt-2">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                                <label class="form-check-label" for="is_active">نشط</label>
                            </div>
                        </div>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i> أضف معلومات المصدر الأساسية.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary" id="addSourceBtn">إضافة</button>
                </div>

                <!-- مؤشر التقدم -->
                <div class="progress mt-3 mx-3 d-none" id="progressContainer">
                    <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%" id="progressBar"></div>
                </div>
                <div class="mt-2 mx-3 mb-3 d-none" id="statusContainer">
                    <small class="text-muted" id="statusText">جاري اكتشاف إعدادات المصدر...</small>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- نافذة تعديل مصدر -->
<div class="modal fade" id="editSourceModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">تعديل مصدر</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="editSourceForm" action="" method="POST">
                <div class="modal-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="edit_name" class="form-label">اسم المصدر <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="edit_name" name="name" required>
                        </div>
                        <div class="col-md-6">
                            <label for="edit_url" class="form-label">رابط المصدر <span class="text-danger">*</span></label>
                            <input type="url" class="form-control" id="edit_url" name="url" required>
                            <small class="form-text text-muted">أدخل رابط الصفحة الرئيسية للموقع الإخباري</small>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="edit_source_type" class="form-label">نوع المصدر</label>
                            <select class="form-select" id="edit_source_type" name="source_type">
                                <option value="website">موقع ويب</option>
                                <option value="rss">RSS</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="edit_is_active" class="form-label">الحالة</label>
                            <div class="form-check form-switch mt-2">
                                <input class="form-check-input" type="checkbox" id="edit_is_active" name="is_active">
                                <label class="form-check-label" for="edit_is_active">نشط</label>
                            </div>
                        </div>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i> قم بتعديل معلومات المصدر الأساسية.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- نافذة حذف مصدر -->
<div class="modal fade" id="deleteSourceModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title">تأكيد حذف المصدر</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف المصدر: <strong id="sourceNameToDelete"></strong>؟</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i> تحذير: لا يمكن التراجع عن هذه العملية.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteSourceForm" action="" method="POST">
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>

{% block scripts %}
<script>
    $(document).ready(function() {
        // تهيئة نافذة تعديل المصدر
        $('.edit-source-btn').click(function() {
            var id = $(this).data('id');
            var name = $(this).data('name');
            var url = $(this).data('url');
            var type = $(this).data('type');
            var active = $(this).data('active');

            $('#edit_name').val(name);
            $('#edit_url').val(url);
            $('#edit_source_type').val(type);
            $('#edit_is_active').prop('checked', active === 'True' || active === true);

            $('#editSourceForm').attr('action', '/sources/edit/' + id);
        });

        // تهيئة نافذة حذف المصدر
        $('.delete-source-btn').click(function() {
            var id = $(this).data('id');
            var name = $(this).data('name');

            $('#sourceNameToDelete').text(name);
            $('#deleteSourceForm').attr('action', '/sources/delete/' + id);
        });
    });
</script>
{% endblock %}

{% endblock %}
