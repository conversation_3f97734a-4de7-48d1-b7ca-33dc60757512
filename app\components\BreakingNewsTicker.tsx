import { Link } from "react-router";
import { Zap, Calendar } from "lucide-react";
import type { NewsArticle } from "~/types";

interface BreakingNewsTickerProps {
  news: NewsArticle[];
}

export function BreakingNewsTicker({ news }: BreakingNewsTickerProps) {
  if (!news || news.length === 0) {
    return null;
  }

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('ar-SA', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    }).format(date);
  };

  return (
    <div className="breaking-news-bar text-white py-2">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center">
          <div className="flex items-center space-x-2 space-x-reverse bg-red-700 px-4 py-1 rounded-full ml-4">
            <Zap className="h-4 w-4 animate-pulse" />
            <span className="font-bold text-sm">أحدث الأخبار</span>
          </div>
          
          <div className="ticker-wrapper flex-1">
            <div className="ticker">
              {news.slice(0, 5).map((article, index) => (
                <div key={article.id} className="ticker-item">
                  <Link 
                    to={`/news/${article.id}`}
                    className="text-white hover:text-yellow-200 transition-colors"
                  >
                    <span className="flex items-center space-x-2 space-x-reverse">
                      <span className="w-2 h-2 bg-white rounded-full animate-pulse"></span>
                      <span className="font-medium">{article.title}</span>
                      <span className="text-red-200 text-sm flex items-center space-x-1 space-x-reverse">
                        <Calendar className="h-3 w-3" />
                        <span>({formatDate(article.publishedDate)})</span>
                      </span>
                    </span>
                  </Link>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
