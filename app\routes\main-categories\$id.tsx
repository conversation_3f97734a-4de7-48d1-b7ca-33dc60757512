import { useState, useEffect } from "react";
import { usePara<PERSON>, useNavigate, <PERSON> } from "react-router";
import { ArrowRight, Edit, Plus, Eye, Trash2, FileText, AlertTriangle } from "lucide-react";
import { Layout } from "~/components/Layout";
import type { MainCategory, Violation, DynamicField } from "~/types";
import { 
  MockMainCategoryService as MainCategoryService,
  MockViolationService as ViolationService,
  MockDynamicFieldService as DynamicFieldService,
  MockNewsService as NewsService
} from "~/lib/mockData";

function MainCategoryDetailsPage() {
  const { id } = useParams();
  const navigate = useNavigate();
  
  const [category, setCategory] = useState<MainCategory | null>(null);
  const [violations, setViolations] = useState<Violation[]>([]);
  const [dynamicFields, setDynamicFields] = useState<DynamicField[]>([]);
  const [newsCount, setNewsCount] = useState(0);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (id) {
      loadData();
    }
  }, [id]);

  const loadData = async () => {
    try {
      setLoading(true);
      
      const [categoryData, violationsData, newsData] = await Promise.all([
        MainCategoryService.getById(id!),
        ViolationService.getByMainCategoryId(id!),
        NewsService.getAll()
      ]);

      if (!categoryData) {
        navigate('/main-categories');
        return;
      }

      setCategory(categoryData);
      setViolations(violationsData);
      setNewsCount(newsData.data.filter(n => n.mainCategoryId === id).length);

      // Load dynamic fields for all violations
      const allFields = await Promise.all(
        violationsData.map(v => DynamicFieldService.getByViolationId(v.id))
      );
      setDynamicFields(allFields.flat());

    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getFieldCount = (violationId: string) => {
    return dynamicFields.filter(f => f.violationId === violationId).length;
  };

  const handleDeleteViolation = async (violationId: string) => {
    if (window.confirm('هل أنت متأكد من حذف هذا الانتهاك؟')) {
      try {
        await ViolationService.delete(violationId);
        setViolations(violations.filter(v => v.id !== violationId));
      } catch (error) {
        console.error('Error deleting violation:', error);
        alert('حدث خطأ أثناء حذف الانتهاك');
      }
    }
  };

  if (loading) {
    return (
      <Layout>
        <div className="flex justify-center items-center min-h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحميل البيانات...</p>
          </div>
        </div>
      </Layout>
    );
  }

  if (!category) {
    return (
      <Layout>
        <div className="text-center py-12">
          <AlertTriangle className="h-12 w-12 text-red-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">التصنيف غير موجود</h3>
          <p className="text-gray-600 mb-4">لم يتم العثور على التصنيف المطلوب</p>
          <Link
            to="/main-categories"
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium inline-flex items-center gap-2 transition-colors"
          >
            <ArrowRight className="h-5 w-5" />
            العودة للتصنيفات
          </Link>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <button
              onClick={() => navigate('/main-categories')}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <ArrowRight className="h-5 w-5" />
            </button>
            <div className="flex-1">
              <h1 className="text-2xl font-bold text-gray-900">{category.name}</h1>
              <p className="text-gray-600 mt-1">{category.description}</p>
            </div>
            <div className="flex gap-2">
              <Link
                to={`/main-categories/${category.id}/edit`}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium flex items-center gap-2 transition-colors"
              >
                <Edit className="h-4 w-4" />
                تعديل
              </Link>
            </div>
          </div>

          {/* Status Badge */}
          <div className="flex items-center gap-4">
            <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
              category.isActive 
                ? 'bg-green-100 text-green-800' 
                : 'bg-red-100 text-red-800'
            }`}>
              {category.isActive ? 'نشط' : 'غير نشط'}
            </span>
          </div>
        </div>

        {/* Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-blue-100">
                <AlertTriangle className="h-6 w-6 text-blue-600" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">الانتهاكات</p>
                <p className="text-2xl font-bold text-gray-900">{violations.length}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-green-100">
                <FileText className="h-6 w-6 text-green-600" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">الأخبار المرتبطة</p>
                <p className="text-2xl font-bold text-gray-900">{newsCount}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-purple-100">
                <FileText className="h-6 w-6 text-purple-600" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">الحقول الديناميكية</p>
                <p className="text-2xl font-bold text-gray-900">{dynamicFields.length}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Violations Section */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold text-gray-900">الانتهاكات الفرعية</h2>
              <Link
                to={`/violations/add?mainCategoryId=${category.id}`}
                className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium flex items-center gap-2 transition-colors"
              >
                <Plus className="h-4 w-4" />
                إضافة انتهاك
              </Link>
            </div>
          </div>

          <div className="p-6">
            {violations.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {violations.map((violation) => (
                  <div key={violation.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                    <div className="flex justify-between items-start mb-3">
                      <div className="flex-1">
                        <h3 className="font-semibold text-gray-900 mb-1">{violation.name}</h3>
                        <p className="text-gray-600 text-sm mb-2">{violation.description}</p>
                        <p className="text-blue-600 text-xs">{violation.relatedRight}</p>
                      </div>
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        violation.isActive 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {violation.isActive ? 'نشط' : 'غير نشط'}
                      </span>
                    </div>

                    <div className="flex justify-between items-center text-sm text-gray-500 mb-3">
                      <span>{getFieldCount(violation.id)} حقل ديناميكي</span>
                    </div>

                    <div className="flex gap-2">
                      <Link
                        to={`/violations/${violation.id}`}
                        className="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 py-1 px-2 rounded text-center text-xs font-medium transition-colors flex items-center justify-center gap-1"
                      >
                        <Eye className="h-3 w-3" />
                        عرض
                      </Link>
                      <Link
                        to={`/violations/${violation.id}/edit`}
                        className="flex-1 bg-blue-100 hover:bg-blue-200 text-blue-700 py-1 px-2 rounded text-center text-xs font-medium transition-colors flex items-center justify-center gap-1"
                      >
                        <Edit className="h-3 w-3" />
                        تعديل
                      </Link>
                      <button
                        onClick={() => handleDeleteViolation(violation.id)}
                        className="flex-1 bg-red-100 hover:bg-red-200 text-red-700 py-1 px-2 rounded text-center text-xs font-medium transition-colors flex items-center justify-center gap-1"
                      >
                        <Trash2 className="h-3 w-3" />
                        حذف
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <AlertTriangle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد انتهاكات</h3>
                <p className="text-gray-600 mb-4">لم يتم إنشاء أي انتهاكات لهذا التصنيف بعد</p>
                <Link
                  to={`/violations/add?mainCategoryId=${category.id}`}
                  className="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium inline-flex items-center gap-2 transition-colors"
                >
                  <Plus className="h-5 w-5" />
                  إضافة انتهاك جديد
                </Link>
              </div>
            )}
          </div>
        </div>
      </div>
    </Layout>
  );
}

export default MainCategoryDetailsPage;
