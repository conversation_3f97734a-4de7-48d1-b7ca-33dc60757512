import { 
  collection, 
  doc, 
  getDocs, 
  getDoc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy, 
  limit,
  startAfter,
  DocumentSnapshot,
  Timestamp,
  writeBatch
} from 'firebase/firestore';
import { db } from './firebase';
import type { 
  NewsArticle, 
  Classification, 
  Source, 
  LegalFile, 
  ClassificationField, 
  ClassificationFieldOption, 
  NewsFieldValue,
  Province,
  SearchFilters,
  SearchResult
} from '~/types';

// Collection Names
export const COLLECTIONS = {
  NEWS: 'news',
  CLASSIFICATIONS: 'classifications', 
  SOURCES: 'sources',
  LEGAL_FILES: 'legal_files',
  CLASSIFICATION_FIELDS: 'classification_fields',
  CLASSIFICATION_FIELD_OPTIONS: 'classification_field_options',
  NEWS_FIELD_VALUES: 'news_field_values',
  PROVINCES: 'provinces',
  USERS: 'users'
} as const;

// Helper function to convert Firestore timestamp to Date
const convertTimestamp = (timestamp: any): Date => {
  if (timestamp?.toDate) {
    return timestamp.toDate();
  }
  return new Date(timestamp);
};

// News Services
export class NewsService {
  static async getAll(filters?: SearchFilters): Promise<SearchResult<NewsArticle>> {
    try {
      let q = query(collection(db, COLLECTIONS.NEWS));
      
      // Apply filters
      if (filters?.classificationId) {
        q = query(q, where('classificationId', '==', filters.classificationId));
      }
      if (filters?.sourceId) {
        q = query(q, where('sourceId', '==', filters.sourceId));
      }
      if (filters?.governorate) {
        q = query(q, where('governorate', '==', filters.governorate));
      }
      if (filters?.status) {
        q = query(q, where('status', '==', filters.status));
      }
      
      // Apply sorting
      const sortField = filters?.sortBy || 'createdAt';
      const sortDirection = filters?.sortOrder || 'desc';
      q = query(q, orderBy(sortField, sortDirection));
      
      // Apply pagination
      if (filters?.limit) {
        q = query(q, limit(filters.limit));
      }
      
      const snapshot = await getDocs(q);
      const items = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: convertTimestamp(doc.data().createdAt),
        updatedAt: convertTimestamp(doc.data().updatedAt),
      })) as NewsArticle[];
      
      return {
        items,
        total: items.length,
        page: filters?.page || 1,
        limit: filters?.limit || 10,
        totalPages: Math.ceil(items.length / (filters?.limit || 10))
      };
    } catch (error) {
      console.error('Error fetching news:', error);
      throw error;
    }
  }

  static async getById(id: string): Promise<NewsArticle | null> {
    try {
      const docRef = doc(db, COLLECTIONS.NEWS, id);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        const data = docSnap.data();
        return {
          id: docSnap.id,
          ...data,
          createdAt: convertTimestamp(data.createdAt),
          updatedAt: convertTimestamp(data.updatedAt),
        } as NewsArticle;
      }
      return null;
    } catch (error) {
      console.error('Error fetching news by ID:', error);
      throw error;
    }
  }

  static async create(newsData: Omit<NewsArticle, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      const docRef = await addDoc(collection(db, COLLECTIONS.NEWS), {
        ...newsData,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now(),
      });
      return docRef.id;
    } catch (error) {
      console.error('Error creating news:', error);
      throw error;
    }
  }

  static async update(id: string, updates: Partial<NewsArticle>): Promise<void> {
    try {
      const docRef = doc(db, COLLECTIONS.NEWS, id);
      await updateDoc(docRef, {
        ...updates,
        updatedAt: Timestamp.now(),
      });
    } catch (error) {
      console.error('Error updating news:', error);
      throw error;
    }
  }

  static async delete(id: string): Promise<void> {
    try {
      const docRef = doc(db, COLLECTIONS.NEWS, id);
      await deleteDoc(docRef);
    } catch (error) {
      console.error('Error deleting news:', error);
      throw error;
    }
  }
}

// Classification Services
export class ClassificationService {
  static async getAll(): Promise<Classification[]> {
    try {
      const q = query(
        collection(db, COLLECTIONS.CLASSIFICATIONS),
        where('isActive', '==', true),
        orderBy('name')
      );
      const snapshot = await getDocs(q);
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: convertTimestamp(doc.data().createdAt),
      })) as Classification[];
    } catch (error) {
      console.error('Error fetching classifications:', error);
      throw error;
    }
  }

  static async getById(id: string): Promise<Classification | null> {
    try {
      const docRef = doc(db, COLLECTIONS.CLASSIFICATIONS, id);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        const data = docSnap.data();
        return {
          id: docSnap.id,
          ...data,
          createdAt: convertTimestamp(data.createdAt),
        } as Classification;
      }
      return null;
    } catch (error) {
      console.error('Error fetching classification by ID:', error);
      throw error;
    }
  }

  static async create(classificationData: Omit<Classification, 'id' | 'createdAt'>): Promise<string> {
    try {
      const docRef = await addDoc(collection(db, COLLECTIONS.CLASSIFICATIONS), {
        ...classificationData,
        createdAt: Timestamp.now(),
      });
      return docRef.id;
    } catch (error) {
      console.error('Error creating classification:', error);
      throw error;
    }
  }
}

// Source Services
export class SourceService {
  static async getAll(): Promise<Source[]> {
    try {
      const q = query(
        collection(db, COLLECTIONS.SOURCES),
        where('isActive', '==', true),
        orderBy('name')
      );
      const snapshot = await getDocs(q);
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: convertTimestamp(doc.data().createdAt),
      })) as Source[];
    } catch (error) {
      console.error('Error fetching sources:', error);
      throw error;
    }
  }

  static async create(sourceData: Omit<Source, 'id' | 'createdAt'>): Promise<string> {
    try {
      const docRef = await addDoc(collection(db, COLLECTIONS.SOURCES), {
        ...sourceData,
        createdAt: Timestamp.now(),
      });
      return docRef.id;
    } catch (error) {
      console.error('Error creating source:', error);
      throw error;
    }
  }
}

// Legal File Services
export class LegalFileService {
  static async getAll(): Promise<LegalFile[]> {
    try {
      const q = query(collection(db, COLLECTIONS.LEGAL_FILES), orderBy('title'));
      const snapshot = await getDocs(q);
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: convertTimestamp(doc.data().createdAt),
      })) as LegalFile[];
    } catch (error) {
      console.error('Error fetching legal files:', error);
      throw error;
    }
  }

  static async create(legalFileData: Omit<LegalFile, 'id' | 'createdAt'>): Promise<string> {
    try {
      const docRef = await addDoc(collection(db, COLLECTIONS.LEGAL_FILES), {
        ...legalFileData,
        createdAt: Timestamp.now(),
      });
      return docRef.id;
    } catch (error) {
      console.error('Error creating legal file:', error);
      throw error;
    }
  }
}

// Classification Field Services
export class ClassificationFieldService {
  static async getByClassificationId(classificationId: string): Promise<ClassificationField[]> {
    try {
      const q = query(
        collection(db, COLLECTIONS.CLASSIFICATION_FIELDS),
        where('classificationId', '==', classificationId),
        orderBy('order')
      );
      const snapshot = await getDocs(q);
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: convertTimestamp(doc.data().createdAt),
      })) as ClassificationField[];
    } catch (error) {
      console.error('Error fetching classification fields:', error);
      throw error;
    }
  }

  static async create(fieldData: Omit<ClassificationField, 'id' | 'createdAt'>): Promise<string> {
    try {
      const docRef = await addDoc(collection(db, COLLECTIONS.CLASSIFICATION_FIELDS), {
        ...fieldData,
        createdAt: Timestamp.now(),
      });
      return docRef.id;
    } catch (error) {
      console.error('Error creating classification field:', error);
      throw error;
    }
  }
}

// Field Options Services
export class FieldOptionService {
  static async getByFieldId(fieldId: string): Promise<ClassificationFieldOption[]> {
    try {
      const q = query(
        collection(db, COLLECTIONS.CLASSIFICATION_FIELD_OPTIONS),
        where('fieldId', '==', fieldId),
        orderBy('order')
      );
      const snapshot = await getDocs(q);
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
      })) as ClassificationFieldOption[];
    } catch (error) {
      console.error('Error fetching field options:', error);
      throw error;
    }
  }

  static async create(optionData: Omit<ClassificationFieldOption, 'id'>): Promise<string> {
    try {
      const docRef = await addDoc(collection(db, COLLECTIONS.CLASSIFICATION_FIELD_OPTIONS), optionData);
      return docRef.id;
    } catch (error) {
      console.error('Error creating field option:', error);
      throw error;
    }
  }
}

// News Field Values Services
export class NewsFieldValueService {
  static async getByNewsId(newsId: string): Promise<NewsFieldValue[]> {
    try {
      const q = query(
        collection(db, COLLECTIONS.NEWS_FIELD_VALUES),
        where('newsId', '==', newsId)
      );
      const snapshot = await getDocs(q);
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
      })) as NewsFieldValue[];
    } catch (error) {
      console.error('Error fetching news field values:', error);
      throw error;
    }
  }

  static async saveFieldValues(newsId: string, fieldValues: Record<string, string>): Promise<void> {
    try {
      const batch = writeBatch(db);
      
      // Delete existing values for this news
      const existingQuery = query(
        collection(db, COLLECTIONS.NEWS_FIELD_VALUES),
        where('newsId', '==', newsId)
      );
      const existingSnapshot = await getDocs(existingQuery);
      existingSnapshot.docs.forEach(doc => {
        batch.delete(doc.ref);
      });
      
      // Add new values
      Object.entries(fieldValues).forEach(([fieldId, value]) => {
        if (value && value.trim()) {
          const newDocRef = doc(collection(db, COLLECTIONS.NEWS_FIELD_VALUES));
          batch.set(newDocRef, {
            newsId,
            fieldId,
            value: value.trim()
          });
        }
      });
      
      await batch.commit();
    } catch (error) {
      console.error('Error saving field values:', error);
      throw error;
    }
  }
}

// Province Services
export class ProvinceService {
  static async getAll(): Promise<Province[]> {
    try {
      const q = query(
        collection(db, COLLECTIONS.PROVINCES),
        where('isActive', '==', true),
        orderBy('name')
      );
      const snapshot = await getDocs(q);
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: convertTimestamp(doc.data().createdAt),
      })) as Province[];
    } catch (error) {
      console.error('Error fetching provinces:', error);
      throw error;
    }
  }

  static async create(provinceData: Omit<Province, 'id' | 'createdAt'>): Promise<string> {
    try {
      const docRef = await addDoc(collection(db, COLLECTIONS.PROVINCES), {
        ...provinceData,
        createdAt: Timestamp.now(),
      });
      return docRef.id;
    } catch (error) {
      console.error('Error creating province:', error);
      throw error;
    }
  }
}


