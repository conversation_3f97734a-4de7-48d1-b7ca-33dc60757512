import { useState, useEffect } from "react";
import { Link, useNavigate } from "react-router";
import { 
  Plus, 
  Search, 
  Filter,
  Edit,
  Trash2,
  Eye,
  Calendar,
  User,
  Tag,
  AlertTriangle,
  FileText,
  Clock
} from "lucide-react";
import { Layout } from "~/components/Layout";
import type { NewsArticle, Classification, Violation, Source, Province } from "~/types";
import {
  MockNewsService as NewsService,
  MockClassificationService as ClassificationService,
  MockViolationService as ViolationService,
  MockSourceService as SourceService,
  MockProvinceService as ProvinceService
} from "~/lib/mockData";

function NewsManagePage() {
  const navigate = useNavigate();
  const [news, setNews] = useState<NewsArticle[]>([]);
  const [classifications, setClassifications] = useState<Classification[]>([]);
  const [violations, setViolations] = useState<Violation[]>([]);
  const [sources, setSources] = useState<Source[]>([]);
  const [provinces, setProvinces] = useState<Province[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedClassification, setSelectedClassification] = useState('');
  const [selectedViolation, setSelectedViolation] = useState('');
  const [selectedSource, setSelectedSource] = useState('');
  const [selectedProvince, setSelectedProvince] = useState('');
  const [deleteConfirm, setDeleteConfirm] = useState<string | null>(null);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [newsData, classificationsData, violationsData, sourcesData, provincesData] = await Promise.all([
        NewsService.getAll(),
        ClassificationService.getAll(),
        ViolationService.getAll(),
        SourceService.getAll(),
        ProvinceService.getAll()
      ]);
      setNews(newsData.items || newsData);
      setClassifications(classificationsData);
      setViolations(violationsData);
      setSources(sourcesData);
      setProvinces(provincesData);
    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    try {
      await NewsService.delete(id);
      setNews(prev => prev.filter(n => n.id !== id));
      setDeleteConfirm(null);
    } catch (error) {
      console.error('Error deleting news:', error);
    }
  };

  const getClassificationName = (classificationId: string) => {
    const classification = classifications.find(c => c.id === classificationId);
    return classification?.name || 'غير محدد';
  };

  const getViolationName = (violationId: string) => {
    const violation = violations.find(v => v.id === violationId);
    return violation?.name || 'غير محدد';
  };

  const getSourceName = (sourceId?: string) => {
    if (!sourceId) return 'غير محدد';
    const source = sources.find(s => s.id === sourceId);
    return source?.name || 'غير محدد';
  };

  const getProvinceName = (provinceId: string) => {
    const province = provinces.find(p => p.id === provinceId);
    return province?.name || 'غير محدد';
  };

  // Filter news based on search and filters
  const filteredNews = news.filter(article => {
    const matchesSearch = article.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         article.content.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesClassification = !selectedClassification || article.classificationId === selectedClassification;
    const matchesViolation = !selectedViolation || article.violationId === selectedViolation;
    const matchesSource = !selectedSource || article.sourceId === selectedSource;
    const matchesProvince = !selectedProvince || article.provinceId === selectedProvince;

    return matchesSearch && matchesClassification && matchesViolation && matchesSource && matchesProvince;
  });

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      draft: { label: 'مسودة', class: 'bg-gray-100 text-gray-800' },
      published: { label: 'منشور', class: 'bg-green-100 text-green-800' },
      archived: { label: 'مؤرشف', class: 'bg-yellow-100 text-yellow-800' }
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.draft;
    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium ${config.class}`}>
        {config.label}
      </span>
    );
  };

  if (loading) {
    return (
      <Layout>
        <div className="flex justify-center items-center min-h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحميل الأخبار...</p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">إدارة الأخبار</h1>
            <p className="text-gray-600 mt-2">
              إدارة وتنظيم الأخبار والتقارير الإعلامية
            </p>
          </div>
          <Link
            to="/news/add"
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg flex items-center gap-2 transition-colors"
          >
            <Plus className="h-5 w-5" />
            إضافة خبر جديد
          </Link>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-blue-100">
                <FileText className="h-6 w-6 text-blue-600" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">إجمالي الأخبار</p>
                <p className="text-2xl font-bold text-gray-900">{news.length}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-green-100">
                <Eye className="h-6 w-6 text-green-600" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">التصنيفات</p>
                <p className="text-2xl font-bold text-gray-900">{classifications.length}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-yellow-100">
                <AlertTriangle className="h-6 w-6 text-yellow-600" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">الانتهاكات</p>
                <p className="text-2xl font-bold text-gray-900">{violations.length}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-purple-100">
                <User className="h-6 w-6 text-purple-600" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">المحافظات</p>
                <p className="text-2xl font-bold text-gray-900">{provinces.length}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow p-6 mb-8">
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {/* Search */}
            <div className="relative md:col-span-2">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <input
                type="text"
                placeholder="البحث في الأخبار..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* Classification Filter */}
            <select
              value={selectedClassification}
              onChange={(e) => setSelectedClassification(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">جميع التصنيفات</option>
              {classifications.map(classification => (
                <option key={classification.id} value={classification.id}>
                  {classification.name}
                </option>
              ))}
            </select>

            {/* Violation Filter */}
            <select
              value={selectedViolation}
              onChange={(e) => setSelectedViolation(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">جميع الانتهاكات</option>
              {violations.map(violation => (
                <option key={violation.id} value={violation.id}>
                  {violation.name}
                </option>
              ))}
            </select>

            {/* Province Filter */}
            <select
              value={selectedProvince}
              onChange={(e) => setSelectedProvince(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">جميع المحافظات</option>
              {provinces.map(province => (
                <option key={province.id} value={province.id}>
                  {province.name}
                </option>
              ))}
            </select>

            {/* Source Filter */}
            <select
              value={selectedSource}
              onChange={(e) => setSelectedSource(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">جميع المصادر</option>
              {sources.map(source => (
                <option key={source.id} value={source.id}>
                  {source.name}
                </option>
              ))}
            </select>
          </div>

          {/* Clear Filters */}
          <div className="mt-4 flex justify-end">
            <button
              onClick={() => {
                setSearchTerm('');
                setSelectedClassification('');
                setSelectedViolation('');
                setSelectedProvince('');
                setSelectedSource('');
              }}
              className="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-md transition-colors"
            >
              مسح الفلاتر
            </button>
          </div>
        </div>

        {/* News List */}
        {filteredNews.length === 0 ? (
          <div className="text-center py-12">
            <FileText className="h-16 w-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد أخبار</h3>
            <p className="text-gray-600 mb-6">
              {searchTerm || selectedClassification || selectedViolation || selectedProvince || selectedSource
                ? 'لا توجد أخبار تطابق معايير البحث'
                : 'ابدأ بإضافة خبر جديد'
              }
            </p>
            <Link
              to="/news/add"
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg inline-flex items-center gap-2"
            >
              <Plus className="h-5 w-5" />
              إضافة خبر جديد
            </Link>
          </div>
        ) : (
          <div className="bg-white rounded-lg shadow overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      العنوان
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      المحافظة
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      التصنيف
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      الانتهاك
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      المصدر
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      التاريخ
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      الإجراءات
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredNews.map((article) => (
                    <tr key={article.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4">
                        <div>
                          <div className="text-sm font-medium text-gray-900 line-clamp-2">
                            {article.title}
                          </div>
                          <div className="text-sm text-gray-500 line-clamp-1 mt-1">
                            {article.content.substring(0, 100)}...
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-900">
                        {getProvinceName(article.provinceId)}
                      </td>
                      <td className="px-6 py-4">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          {getClassificationName(article.classificationId)}
                        </span>
                      </td>
                      <td className="px-6 py-4">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                          {getViolationName(article.violationId)}
                        </span>
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-900">
                        {getSourceName(article.sourceId)}
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-500">
                        {formatDate(article.date)}
                      </td>
                      <td className="px-6 py-4 text-sm font-medium">
                        <div className="flex items-center gap-2">
                          <Link
                            to={`/news/${article.id}`}
                            className="text-blue-600 hover:text-blue-900 transition-colors"
                            title="عرض"
                          >
                            <Eye className="h-4 w-4" />
                          </Link>
                          <Link
                            to={`/news/${article.id}/edit`}
                            className="text-green-600 hover:text-green-900 transition-colors"
                            title="تعديل"
                          >
                            <Edit className="h-4 w-4" />
                          </Link>
                          <button
                            onClick={() => setDeleteConfirm(article.id)}
                            className="text-red-600 hover:text-red-900 transition-colors"
                            title="حذف"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* Delete Confirmation Modal */}
        {deleteConfirm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
              <div className="flex items-center mb-4">
                <div className="p-3 rounded-full bg-red-100 ml-3">
                  <AlertTriangle className="h-6 w-6 text-red-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900">تأكيد الحذف</h3>
              </div>
              
              <p className="text-gray-600 mb-6">
                هل أنت متأكد من حذف هذا الخبر؟ هذا الإجراء لا يمكن التراجع عنه.
              </p>
              
              <div className="flex gap-3 justify-end">
                <button
                  onClick={() => setDeleteConfirm(null)}
                  className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
                >
                  إلغاء
                </button>
                <button
                  onClick={() => handleDelete(deleteConfirm)}
                  className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md transition-colors"
                >
                  حذف
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
}

export default NewsManagePage;
