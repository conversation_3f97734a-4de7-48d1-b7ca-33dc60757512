import { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router';
import { ArrowRight, Save, AlertCircle } from 'lucide-react';
import { MainCategory, ViolationForm } from '~/types';
import { MockViolationService as ViolationService, MockMainCategoryService as MainCategoryService } from '~/lib/mockData';

export default function AddViolation() {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [mainCategories, setMainCategories] = useState<MainCategory[]>([]);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<ViolationForm>({
    name: '',
    description: '',
    relatedRight: '',
    mainCategoryId: ''
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    loadMainCategories();
  }, []);

  const loadMainCategories = async () => {
    try {
      const categories = await MainCategoryService.getAll();
      setMainCategories(categories.filter(c => c.isActive));

      // Pre-select category if provided in URL
      const mainCategoryId = searchParams.get('mainCategoryId');
      if (mainCategoryId) {
        setFormData(prev => ({ ...prev, mainCategoryId }));
      }
    } catch (error) {
      console.error('Error loading main categories:', error);
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'اسم الانتهاك مطلوب';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'وصف الانتهاك مطلوب';
    }

    if (!formData.relatedRight.trim()) {
      newErrors.relatedRight = 'الحق المرتبط مطلوب';
    }

    if (!formData.mainCategoryId) {
      newErrors.mainCategoryId = 'التصنيف الرئيسي مطلوب';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      await ViolationService.create({
        ...formData,
        isActive: true
      });
      navigate('/violations');
    } catch (error) {
      console.error('Error creating violation:', error);
      setErrors({ submit: 'حدث خطأ أثناء إضافة الانتهاك' });
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof ViolationForm, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center">
            <button
              onClick={() => navigate('/violations')}
              className="ml-4 p-2 text-gray-400 hover:text-gray-600"
            >
              <ArrowRight className="h-5 w-5" />
            </button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">إضافة انتهاك جديد</h1>
              <p className="mt-2 text-gray-600">إضافة انتهاك فرعي جديد مرتبط بأحد التصنيفات الرئيسية</p>
            </div>
          </div>
        </div>

        {/* Form */}
        <div className="bg-white shadow rounded-lg">
          <form onSubmit={handleSubmit} className="space-y-6 p-6">
            {errors.submit && (
              <div className="rounded-md bg-red-50 p-4">
                <div className="flex">
                  <AlertCircle className="h-5 w-5 text-red-400" />
                  <div className="mr-3">
                    <p className="text-sm text-red-800">{errors.submit}</p>
                  </div>
                </div>
              </div>
            )}

            {/* Main Category Selection */}
            <div>
              <label htmlFor="mainCategoryId" className="block text-sm font-medium text-gray-700">
                التصنيف الرئيسي *
              </label>
              <select
                id="mainCategoryId"
                value={formData.mainCategoryId}
                onChange={(e) => handleInputChange('mainCategoryId', e.target.value)}
                className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${
                  errors.mainCategoryId ? 'border-red-300' : 'border-gray-300'
                }`}
              >
                <option value="">اختر التصنيف الرئيسي</option>
                {mainCategories.map(category => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
              {errors.mainCategoryId && (
                <p className="mt-2 text-sm text-red-600">{errors.mainCategoryId}</p>
              )}
            </div>

            {/* Violation Name */}
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                اسم الانتهاك *
              </label>
              <input
                type="text"
                id="name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${
                  errors.name ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="مثال: الاعتقال التعسفي"
              />
              {errors.name && (
                <p className="mt-2 text-sm text-red-600">{errors.name}</p>
              )}
            </div>

            {/* Description */}
            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                وصف الانتهاك *
              </label>
              <textarea
                id="description"
                rows={4}
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${
                  errors.description ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="وصف تفصيلي للانتهاك وطبيعته..."
              />
              {errors.description && (
                <p className="mt-2 text-sm text-red-600">{errors.description}</p>
              )}
            </div>

            {/* Related Right */}
            <div>
              <label htmlFor="relatedRight" className="block text-sm font-medium text-gray-700">
                الحق المرتبط *
              </label>
              <input
                type="text"
                id="relatedRight"
                value={formData.relatedRight}
                onChange={(e) => handleInputChange('relatedRight', e.target.value)}
                className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${
                  errors.relatedRight ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="مثال: الحق في الحرية والأمان الشخصي"
              />
              {errors.relatedRight && (
                <p className="mt-2 text-sm text-red-600">{errors.relatedRight}</p>
              )}
            </div>

            {/* Submit Button */}
            <div className="flex justify-end space-x-3 space-x-reverse">
              <button
                type="button"
                onClick={() => navigate('/violations')}
                className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                إلغاء
              </button>
              <button
                type="submit"
                disabled={loading}
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
              >
                {loading ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                ) : (
                  <Save className="h-4 w-4 ml-2" />
                )}
                حفظ الانتهاك
              </button>
            </div>
          </form>
        </div>

        {/* Help Section */}
        <div className="mt-8 bg-blue-50 rounded-lg p-6">
          <h3 className="text-lg font-medium text-blue-900 mb-4">إرشادات إضافة الانتهاكات</h3>
          <div className="space-y-3 text-sm text-blue-800">
            <div>
              <strong>اسم الانتهاك:</strong> يجب أن يكون واضحاً ومحدداً (مثال: الاعتقال التعسفي، التعذيب، التمييز ضد المرأة)
            </div>
            <div>
              <strong>الوصف:</strong> وصف تفصيلي يوضح طبيعة الانتهاك وكيفية حدوثه
            </div>
            <div>
              <strong>الحق المرتبط:</strong> الحق الأساسي الذي يتم انتهاكه (مثال: الحق في الحرية، الحق في عدم التمييز)
            </div>
            <div>
              <strong>التصنيف الرئيسي:</strong> اختر التصنيف الرئيسي المناسب الذي ينتمي إليه هذا الانتهاك
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
