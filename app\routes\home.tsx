import { Layout } from "~/components/Layout";
import { BreakingNewsTicker } from "~/components/BreakingNewsTicker";
import { NewsCard } from "~/components/NewsCard";
import { Link } from "react-router";
import {
  Plus,
  FileText,
  BarChart3,
  Calendar,
  Newspaper,
  Tags,
  MapPin,
  TrendingUp
} from "lucide-react";

export function meta() {
  return [
    { title: "نظام الرصد الإعلامي  " },
    { name: "description", content: "منصة متكاملة لرصد وتوثيق انتهاكات حقوق الإنسان في العراق" },
  ];
}

// Mock data - في التطبيق الحقيقي سيتم جلب البيانات من Firebase
const mockStats = {
  totalArticles: 1247,
  totalCategories: 8,
  totalProvinces: 15,
  articlesToday: 12
};

const mockLatestNews = [
  {
    id: "1",
    title: "تقرير جديد حول انتهاكات حقوق الإنسان في بغداد",
    content: "كشف تقرير حديث عن ارتفاع في عدد انتهاكات حقوق الإنسان في العاصمة بغداد خلال الشهر الماضي...",
    publishedDate: new Date(),
    province: { id: "1", name: "بغداد", createdAt: new Date() },
    source: { id: "1", name: "وكالة الأنباء العراقية", type: "website" as const, createdAt: new Date() },
    category: { id: "1", name: "انتهاكات حقوق الإنسان", dynamicFields: [], createdAt: new Date(), updatedAt: new Date() },
    viewCount: 156,
    tags: ["حقوق الإنسان", "بغداد", "انتهاكات"],
    dynamicData: {},
    createdAt: new Date(),
    updatedAt: new Date(),
    createdBy: "user1",
    status: "published" as const,
    isPublic: true
  },
  {
    id: "2",
    title: "حملة توعية حول حقوق المرأة في البصرة",
    content: "انطلقت اليوم حملة توعية واسعة حول حقوق المرأة في محافظة البصرة بمشاركة منظمات المجتمع المدني...",
    publishedDate: new Date(Date.now() - 86400000),
    province: { id: "2", name: "البصرة", createdAt: new Date() },
    source: { id: "2", name: "إذاعة البصرة", type: "radio" as const, createdAt: new Date() },
    category: { id: "2", name: "حقوق المرأة", dynamicFields: [], createdAt: new Date(), updatedAt: new Date() },
    viewCount: 89,
    tags: ["حقوق المرأة", "البصرة", "توعية"],
    dynamicData: {},
    createdAt: new Date(),
    updatedAt: new Date(),
    createdBy: "user2",
    status: "published" as const,
    isPublic: true
  }
];

export default function Home() {
  const today = new Intl.DateTimeFormat('ar-SA', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }).format(new Date());

  return (
    <Layout>
      {/* Breaking News Ticker */}
      <BreakingNewsTicker news={mockLatestNews} />

      {/* Hero Section */}
      <div className="hero-section text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
            <div className="text-center lg:text-right order-2 lg:order-1">
              <h1 className="text-4xl lg:text-5xl font-bold mb-4">
                <img
                  src="/logo2.png"
                  alt="شعار النظام"
                  className="inline-block h-12 w-auto ml-3 small-logo floating-logo"
                />
                نظام الرصد الإعلامي
              </h1>
              <p className="text-xl mb-8 text-blue-100">
                منصة متكاملة لرصد وتوثيق انتهاكات حقوق الإنسان في العراق
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                <Link
                  to="/news/add"
                  className="inline-flex items-center px-6 py-3 bg-white text-blue-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <Plus className="h-5 w-5 ml-2" />
                  إضافة خبر جديد
                </Link>
                <Link
                  to="/news"
                  className="inline-flex items-center px-6 py-3 border-2 border-white text-white font-semibold rounded-lg hover:bg-white hover:text-blue-600 transition-colors"
                >
                  <FileText className="h-5 w-5 ml-2" />
                  عرض الأخبار
                </Link>
              </div>
            </div>

            <div className="text-center order-1 lg:order-2">
              <div className="relative">
                <img
                  src="/logo2.png"
                  alt="شعار نظام الرصد الإعلامي"
                  className="mx-auto h-64 w-auto floating-logo opacity-90"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Statistics Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-white rounded-lg shadow-md p-6 text-center hover-card">
            <div className="icon-container mb-4">
              <Newspaper className="h-8 w-8 text-blue-600" />
            </div>
            <h3 className="text-3xl font-bold text-gray-900 mb-2">{mockStats.totalArticles}</h3>
            <p className="text-gray-600">إجمالي الأخبار</p>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6 text-center hover-card">
            <div className="icon-container mb-4">
              <Tags className="h-8 w-8 text-green-600" />
            </div>
            <h3 className="text-3xl font-bold text-gray-900 mb-2">{mockStats.totalCategories}</h3>
            <p className="text-gray-600">التصنيفات</p>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6 text-center hover-card">
            <div className="icon-container mb-4">
              <MapPin className="h-8 w-8 text-purple-600" />
            </div>
            <h3 className="text-3xl font-bold text-gray-900 mb-2">{mockStats.totalProvinces}</h3>
            <p className="text-gray-600">المحافظات</p>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6 text-center hover-card">
            <div className="icon-container mb-4">
              <Calendar className="h-8 w-8 text-orange-600" />
            </div>
            <h3 className="text-3xl font-bold text-gray-900 mb-2">{mockStats.articlesToday}</h3>
            <p className="text-gray-600">أخبار اليوم</p>
            <p className="text-xs text-gray-500 mt-1">{today}</p>
          </div>
        </div>
      </div>

      {/* Latest News Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="section-header flex items-center justify-between mb-8">
          <h2 className="text-2xl font-bold text-gray-900 flex items-center">
            <TrendingUp className="h-6 w-6 ml-2 text-blue-600" />
            أحدث الأخبار
          </h2>
          <Link
            to="/news"
            className="text-blue-600 hover:text-blue-800 font-medium transition-colors"
          >
            عرض كل الأخبار ←
          </Link>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {mockLatestNews.map((article) => (
            <NewsCard key={article.id} article={article} />
          ))}
        </div>
      </div>

      {/* Main Sections */}
      <div className="bg-gray-100 py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="section-header text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900">الأقسام الرئيسية</h2>
            <p className="text-gray-600 mt-2">إدارة شاملة لجميع جوانب النظام</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Link to="/news" className="group">
              <div className="bg-white rounded-lg shadow-md p-6 text-center hover-card group-hover:shadow-lg transition-all">
                <div className="icon-container mb-4 group-hover:scale-110 transition-transform">
                  <Newspaper className="h-8 w-8 text-blue-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">إدارة الأخبار</h3>
                <p className="text-gray-600 text-sm">إضافة وتعديل وعرض الأخبار بكافة تصنيفاتها</p>
              </div>
            </Link>

            <Link to="/main-categories" className="group">
              <div className="bg-white rounded-lg shadow-md p-6 text-center hover-card group-hover:shadow-lg transition-all">
                <div className="icon-container mb-4 group-hover:scale-110 transition-transform">
                  <Tags className="h-8 w-8 text-green-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">التصنيفات الرئيسية</h3>
                <p className="text-gray-600 text-sm">إدارة الحقوق الأساسية والانتهاكات</p>
              </div>
            </Link>

            <Link to="/provinces" className="group">
              <div className="bg-white rounded-lg shadow-md p-6 text-center hover-card group-hover:shadow-lg transition-all">
                <div className="icon-container mb-4 group-hover:scale-110 transition-transform">
                  <MapPin className="h-8 w-8 text-purple-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">إدارة المحافظات</h3>
                <p className="text-gray-600 text-sm">إضافة وتعديل المحافظات في النظام</p>
              </div>
            </Link>

            <Link to="/reports" className="group">
              <div className="bg-white rounded-lg shadow-md p-6 text-center hover-card group-hover:shadow-lg transition-all">
                <div className="icon-container mb-4 group-hover:scale-110 transition-transform">
                  <BarChart3 className="h-8 w-8 text-orange-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">التقارير والإحصائيات</h3>
                <p className="text-gray-600 text-sm">عرض إحصائيات شاملة وتصدير التقارير</p>
              </div>
            </Link>
          </div>
        </div>
      </div>
    </Layout>
  );
}
