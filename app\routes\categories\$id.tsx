import { useState, useEffect } from "react";
import { use<PERSON>ara<PERSON>, useNavigate, Link } from "react-router";
import { 
  ArrowRight, 
  Edit, 
  Settings,
  FileText,
  AlertTriangle,
  ExternalLink,
  Calendar,
  Users,
  Baby,
  Shield
} from "lucide-react";
import { Layout } from "~/components/Layout";
import type { Classification, LegalFile, Violation, NewsArticle } from "~/types";
import {
  MockClassificationService as ClassificationService,
  MockLegalFileService as LegalFileService,
  MockViolationService as ViolationService,
  MockNewsService as NewsService
} from "~/lib/mockData";

// Icon mapping
const iconMap = {
  AlertTriangle,
  Users,
  Baby,
  Shield,
  FileText,
  Settings
};

export default function ClassificationDetailsPage() {
  const { id } = useParams();
  const navigate = useNavigate();
  
  const [classification, setClassification] = useState<Classification | null>(null);
  const [legalFile, setLegalFile] = useState<LegalFile | null>(null);
  const [violations, setViolations] = useState<Violation[]>([]);
  const [newsCount, setNewsCount] = useState(0);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (id) {
      loadData();
    }
  }, [id]);

  const loadData = async () => {
    if (!id) return;
    
    try {
      setLoading(true);
      const classificationData = await ClassificationService.getById(id);
      
      if (classificationData) {
        setClassification(classificationData);
        
        const [legalFileData, violationsData, newsData] = await Promise.all([
          LegalFileService.getById(classificationData.legalFileId),
          ViolationService.getByClassificationId(id),
          NewsService.getAll()
        ]);

        setLegalFile(legalFileData);
        setViolations(violationsData);
        // Count news articles for this classification
        const classificationNews = newsData.filter(news => news.classificationId === id);
        setNewsCount(classificationNews.length);
      }
    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getIcon = (iconName: string) => {
    const IconComponent = iconMap[iconName as keyof typeof iconMap] || FileText;
    return IconComponent;
  };

  if (loading) {
    return (
      <Layout>
        <div className="flex justify-center items-center min-h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحميل التفاصيل...</p>
          </div>
        </div>
      </Layout>
    );
  }

  if (!classification) {
    return (
      <Layout>
        <div className="text-center py-12">
          <AlertTriangle className="h-16 w-16 text-red-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">التصنيف غير موجود</h3>
          <button
            onClick={() => navigate('/categories')}
            className="text-blue-600 hover:text-blue-700"
          >
            العودة إلى التصنيفات
          </button>
        </div>
      </Layout>
    );
  }

  const IconComponent = getIcon(classification.icon);

  return (
    <Layout>
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center">
            <button
              onClick={() => navigate('/categories')}
              className="ml-4 p-2 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <ArrowRight className="h-6 w-6" />
            </button>
            <div className="flex items-center gap-4">
              <div 
                className="p-4 rounded-full"
                style={{ backgroundColor: `${classification.color}20` }}
              >
                <IconComponent 
                  className="h-8 w-8" 
                  style={{ color: classification.color }}
                />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">{classification.name}</h1>
                <p className="text-gray-600 mt-1">{classification.description}</p>
              </div>
            </div>
          </div>
          
          <Link
            to={`/categories/${classification.id}/edit`}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg flex items-center gap-2 transition-colors"
          >
            <Edit className="h-5 w-5" />
            تعديل التصنيف
          </Link>
        </div>

        {/* Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-blue-100">
                <FileText className="h-6 w-6 text-blue-600" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">عدد الأخبار</p>
                <p className="text-2xl font-bold text-gray-900">{newsCount}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-green-100">
                <Settings className="h-6 w-6 text-green-600" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">عدد الانتهاكات</p>
                <p className="text-2xl font-bold text-gray-900">{violations.length}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className={`p-3 rounded-full ${classification.isActive ? 'bg-green-100' : 'bg-gray-100'}`}>
                <div className={`h-6 w-6 rounded-full ${classification.isActive ? 'bg-green-500' : 'bg-gray-400'}`}></div>
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">الحالة</p>
                <p className="text-lg font-bold text-gray-900">
                  {classification.isActive ? 'نشط' : 'غير نشط'}
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Legal File Info */}
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                <FileText className="h-5 w-5" />
                الملف الحقوقي المرتبط
              </h3>
            </div>
            <div className="p-6">
              {legalFile ? (
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">{legalFile.title}</h4>
                    <p className="text-sm text-gray-600">
                      <strong>الجهة المعتمدة:</strong> {legalFile.approvedBy}
                    </p>
                    <p className="text-sm text-gray-600">
                      <strong>تاريخ الاعتماد:</strong> {legalFile.date}
                    </p>
                  </div>
                  
                  {legalFile.link && (
                    <a
                      href={legalFile.link}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-700 text-sm"
                    >
                      <ExternalLink className="h-4 w-4" />
                      عرض الملف الكامل
                    </a>
                  )}
                </div>
              ) : (
                <p className="text-gray-500">لا يوجد ملف حقوقي مرتبط</p>
              )}
            </div>
          </div>

          {/* Violations Overview */}
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                <AlertTriangle className="h-5 w-5" />
                الانتهاكات
              </h3>
            </div>
            <div className="p-6">
              {violations.length === 0 ? (
                <div className="text-center py-8">
                  <AlertTriangle className="h-12 w-12 text-gray-300 mx-auto mb-3" />
                  <p className="text-gray-500 mb-4">لا توجد انتهاكات مسجلة</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {violations.map((violation) => (
                    <div key={violation.id} className="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0">
                      <div className="flex items-center gap-3">
                        <AlertTriangle className="h-4 w-4 text-red-500" />
                        <div>
                          <span className="font-medium text-gray-900">{violation.name}</span>
                          <p className="text-sm text-gray-600 mt-1">{violation.relatedRight}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Classification Info */}
        <div className="bg-white rounded-lg shadow mt-8">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">معلومات التصنيف</h3>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">عدد الأخبار</p>
                <p className="text-gray-900 font-semibold">
                  {newsCount} خبر
                </p>
              </div>

              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">الحالة</p>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  نشط
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white rounded-lg shadow mt-8">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">إجراءات سريعة</h3>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Link
                to={`/categories/${classification.id}/edit`}
                className="flex items-center gap-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <Edit className="h-5 w-5 text-blue-600" />
                <div>
                  <p className="font-medium text-gray-900">تعديل التصنيف</p>
                  <p className="text-sm text-gray-600">تحديث المعلومات الأساسية</p>
                </div>
              </Link>
              
              <Link
                to={`/violations?classification=${classification.id}`}
                className="flex items-center gap-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <AlertTriangle className="h-5 w-5 text-red-600" />
                <div>
                  <p className="font-medium text-gray-900">إدارة الانتهاكات</p>
                  <p className="text-sm text-gray-600">إضافة وتعديل الانتهاكات</p>
                </div>
              </Link>
              
              <Link
                to={`/news?classification=${classification.id}`}
                className="flex items-center gap-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <FileText className="h-5 w-5 text-purple-600" />
                <div>
                  <p className="font-medium text-gray-900">عرض الأخبار</p>
                  <p className="text-sm text-gray-600">الأخبار المصنفة تحت هذا التصنيف</p>
                </div>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}
