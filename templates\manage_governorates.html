{% extends "layout.html" %}

{% block content %}
<div class="container py-4">
    <!-- شريط التنقل -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('index') }}">الرئيسية</a></li>
            <li class="breadcrumb-item active" aria-current="page">إدارة المحافظات</li>
        </ol>
    </nav>

    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0"><i class="fas fa-map-marked-alt me-2"></i>إدارة المحافظات</h2>
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addGovernorateModal">
            <i class="fas fa-plus-circle me-1"></i> إضافة محافظة جديدة
        </button>
    </div>

    <div class="card shadow-sm">
        <div class="card-body">
            {% if governorates %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>اسم المحافظة</th>
                                <th>عدد الأخبار</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for governorate in governorates %}
                                <tr>
                                    <td>{{ loop.index }}</td>
                                    <td>{{ governorate.name }}</td>
                                    <td>{{ governorate.news_count }}</td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-warning edit-governorate"
                                                data-bs-toggle="modal"
                                                data-bs-target="#editGovernorateModal"
                                                data-id="{{ governorate.id }}"
                                                data-name="{{ governorate.name }}">
                                            <i class="fas fa-edit"></i> تعديل
                                        </button>
                                        <button type="button" class="btn btn-sm btn-danger delete-governorate"
                                                data-bs-toggle="modal"
                                                data-bs-target="#deleteGovernorateModal"
                                                data-id="{{ governorate.id }}"
                                                data-name="{{ governorate.name }}">
                                            <i class="fas fa-trash"></i> حذف
                                        </button>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i> لا توجد محافظات مسجلة حالياً.
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Modal for Add Governorate -->
<div class="modal fade" id="addGovernorateModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">إضافة محافظة جديدة</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="addGovernorateForm" action="#" method="POST">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="governorate_name" class="form-label">اسم المحافظة</label>
                        <input type="text" class="form-control" id="governorate_name" name="name" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal for Edit Governorate -->
<div class="modal fade" id="editGovernorateModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-warning text-white">
                <h5 class="modal-title">تعديل المحافظة</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="editGovernorateForm" action="#" method="POST">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="edit_governorate_name" class="form-label">اسم المحافظة</label>
                        <input type="text" class="form-control" id="edit_governorate_name" name="name" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-warning">تعديل</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal for Delete Governorate -->
<div class="modal fade" id="deleteGovernorateModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title">حذف المحافظة</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف المحافظة: <strong id="governorateNameToDelete"></strong>؟</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i> تحذير: سيتم حذف جميع الأخبار المرتبطة بهذه المحافظة.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteGovernorateForm" action="#" method="POST">
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // تهيئة نافذة تعديل المحافظة
        $('.edit-governorate').click(function() {
            var governorateId = $(this).data('id');
            var governorateName = $(this).data('name');

            $('#edit_governorate_name').val(governorateName);
            $('#editGovernorateForm').attr('action', '/governorates/edit/' + governorateId);
        });

        // تهيئة نافذة حذف المحافظة
        $('.delete-governorate').click(function() {
            var governorateId = $(this).data('id');
            var governorateName = $(this).data('name');

            $('#governorateNameToDelete').text(governorateName);
            $('#deleteGovernorateForm').attr('action', '/governorates/delete/' + governorateId);
        });

        // تهيئة نافذة إضافة محافظة
        $('#addGovernorateForm').attr('action', '/governorates/add');
    });
</script>
{% endblock %}
