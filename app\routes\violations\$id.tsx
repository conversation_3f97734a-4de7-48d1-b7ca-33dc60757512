import { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate, Link } from 'react-router';
import { ArrowRight, Edit, Trash2, Plus, Eye, Settings } from 'lucide-react';
import { Violation, MainCategory, DynamicField } from '~/types';
import { 
  MockViolationService as ViolationService, 
  MockMainCategoryService as MainCategoryService,
  MockDynamicFieldService as DynamicFieldService 
} from '~/lib/mockData';

export default function ViolationDetails() {
  const { id } = useParams();
  const navigate = useNavigate();
  const [violation, setViolation] = useState<Violation | null>(null);
  const [mainCategory, setMainCategory] = useState<MainCategory | null>(null);
  const [dynamicFields, setDynamicFields] = useState<DynamicField[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (id) {
      loadData();
    }
  }, [id]);

  const loadData = async () => {
    try {
      setLoading(true);
      const violationData = await ViolationService.getById(id!);
      
      if (!violationData) {
        navigate('/violations');
        return;
      }

      setViolation(violationData);

      const [categoryData, fieldsData] = await Promise.all([
        MainCategoryService.getById(violationData.mainCategoryId),
        DynamicFieldService.getByViolationId(violationData.id)
      ]);

      setMainCategory(categoryData);
      setDynamicFields(fieldsData);
    } catch (error) {
      console.error('Error loading violation details:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (confirm('هل أنت متأكد من حذف هذا الانتهاك؟ سيتم حذف جميع الحقول الديناميكية المرتبطة به أيضاً.')) {
      try {
        await ViolationService.delete(id!);
        navigate('/violations');
      } catch (error) {
        console.error('Error deleting violation:', error);
      }
    }
  };

  const getFieldTypeLabel = (type: string) => {
    const types: Record<string, string> = {
      'text': 'نص',
      'textarea': 'نص طويل',
      'number': 'رقم',
      'select': 'قائمة منسدلة'
    };
    return types[type] || type;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جار تحميل تفاصيل الانتهاك...</p>
        </div>
      </div>
    );
  }

  if (!violation) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">الانتهاك غير موجود</p>
          <Link to="/violations" className="text-blue-600 hover:text-blue-800 mt-2 inline-block">
            العودة إلى قائمة الانتهاكات
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <button
                onClick={() => navigate('/violations')}
                className="ml-4 p-2 text-gray-400 hover:text-gray-600"
              >
                <ArrowRight className="h-5 w-5" />
              </button>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">{violation.name}</h1>
                <p className="mt-2 text-gray-600">تفاصيل الانتهاك والحقول الديناميكية المرتبطة به</p>
              </div>
            </div>
            <div className="flex items-center space-x-3 space-x-reverse">
              <Link
                to={`/violations/${violation.id}/edit`}
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
              >
                <Edit className="h-4 w-4 ml-2" />
                تعديل
              </Link>
              <button
                onClick={handleDelete}
                className="inline-flex items-center px-4 py-2 border border-red-300 rounded-md shadow-sm text-sm font-medium text-red-700 bg-white hover:bg-red-50"
              >
                <Trash2 className="h-4 w-4 ml-2" />
                حذف
              </button>
            </div>
          </div>
        </div>

        {/* Violation Details */}
        <div className="bg-white shadow rounded-lg mb-8">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">معلومات الانتهاك</h2>
          </div>
          <div className="px-6 py-4">
            <dl className="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
              <div>
                <dt className="text-sm font-medium text-gray-500">اسم الانتهاك</dt>
                <dd className="mt-1 text-sm text-gray-900">{violation.name}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">التصنيف الرئيسي</dt>
                <dd className="mt-1 text-sm text-gray-900">
                  {mainCategory ? (
                    <Link 
                      to={`/main-categories/${mainCategory.id}`}
                      className="text-blue-600 hover:text-blue-800"
                    >
                      {mainCategory.name}
                    </Link>
                  ) : (
                    'غير محدد'
                  )}
                </dd>
              </div>
              <div className="sm:col-span-2">
                <dt className="text-sm font-medium text-gray-500">وصف الانتهاك</dt>
                <dd className="mt-1 text-sm text-gray-900">{violation.description}</dd>
              </div>
              <div className="sm:col-span-2">
                <dt className="text-sm font-medium text-gray-500">الحق المرتبط</dt>
                <dd className="mt-1 text-sm text-gray-900">{violation.relatedRight}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">الحالة</dt>
                <dd className="mt-1">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    violation.isActive 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {violation.isActive ? 'نشط' : 'غير نشط'}
                  </span>
                </dd>
              </div>
            </dl>
          </div>
        </div>

        {/* Dynamic Fields */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-medium text-gray-900">
                الحقول الديناميكية ({dynamicFields.length})
              </h2>
              <button
                onClick={() => {/* TODO: Add dynamic field */}}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200"
              >
                <Plus className="h-4 w-4 ml-1" />
                إضافة حقل
              </button>
            </div>
          </div>
          
          {dynamicFields.length > 0 ? (
            <div className="divide-y divide-gray-200">
              {dynamicFields.map((field) => (
                <div key={field.id} className="px-6 py-4">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center">
                        <h3 className="text-sm font-medium text-gray-900">{field.label}</h3>
                        <span className={`ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          field.isRequired 
                            ? 'bg-red-100 text-red-800' 
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {field.isRequired ? 'مطلوب' : 'اختياري'}
                        </span>
                        <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          {getFieldTypeLabel(field.type)}
                        </span>
                      </div>
                      <p className="mt-1 text-sm text-gray-500">
                        اسم الحقل: {field.name}
                      </p>
                      {field.placeholder && (
                        <p className="mt-1 text-sm text-gray-500">
                          النص التوضيحي: {field.placeholder}
                        </p>
                      )}
                      {field.options && field.options.length > 0 && (
                        <div className="mt-2">
                          <p className="text-sm text-gray-500">الخيارات المتاحة:</p>
                          <div className="mt-1 flex flex-wrap gap-1">
                            {field.options.map((option, index) => (
                              <span key={index} className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800">
                                {option}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <button
                        onClick={() => {/* TODO: Edit field */}}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        <Settings className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => {/* TODO: Delete field */}}
                        className="text-red-600 hover:text-red-900"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="px-6 py-12 text-center">
              <p className="text-gray-500">لا توجد حقول ديناميكية مرتبطة بهذا الانتهاك</p>
              <button
                onClick={() => {/* TODO: Add first field */}}
                className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
              >
                <Plus className="h-4 w-4 ml-2" />
                إضافة أول حقل ديناميكي
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
