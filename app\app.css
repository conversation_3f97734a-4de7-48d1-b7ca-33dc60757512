@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;800&display=swap');
@import "tailwindcss";

@theme {
  --font-sans: "<PERSON><PERSON><PERSON>", "<PERSON>", ui-sans-serif, system-ui, sans-serif,
    "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}

html,
body {
  @apply bg-gray-50 dark:bg-gray-950;
  font-family: '<PERSON><PERSON><PERSON>', sans-serif;
  direction: rtl;

  @media (prefers-color-scheme: dark) {
    color-scheme: dark;
  }
}

/* Custom styles for Arabic RTL layout */
.rtl {
  direction: rtl;
  text-align: right;
}

/* Hero Section */
.hero-section {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* Breaking News Ticker */
.breaking-news-bar {
  background: linear-gradient(90deg, #dc2626, #f97316);
  overflow: hidden;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.ticker-wrapper {
  width: 100%;
  overflow: hidden;
}

.ticker {
  display: flex;
  animation: ticker 60s linear infinite;
  white-space: nowrap;
}

.ticker-item {
  margin-right: 80px;
}

@keyframes ticker {
  0% { transform: translateX(100%); }
  100% { transform: translateX(-100%); }
}

.ticker:hover {
  animation-play-state: paused;
}

/* Logo animations */
.floating-logo {
  animation: float-logo 3s ease-in-out infinite;
}

@keyframes float-logo {
  0% { transform: translateY(0); }
  50% { transform: translateY(-15px); }
  100% { transform: translateY(0); }
}

.small-logo {
  filter: drop-shadow(0 0 3px rgba(255, 255, 255, 0.8));
  vertical-align: middle;
  display: inline-block;
}

.small-logo.floating-logo {
  animation: float-small-logo 2s ease-in-out infinite;
}

@keyframes float-small-logo {
  0% { transform: translateY(0) rotate(0deg); }
  50% { transform: translateY(-5px) rotate(10deg); }
  100% { transform: translateY(0) rotate(0deg); }
}

/* Card hover effects */
.hover-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0,0,0,0.1) !important;
}

/* Icon containers */
.icon-container {
  width: 80px;
  height: 80px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(59, 130, 246, 0.1);
  border-radius: 50%;
  transition: all 0.3s ease;
}

.icon-container:hover {
  transform: scale(1.1);
  background-color: rgba(59, 130, 246, 0.2);
}

/* Section headers */
.section-header h2 {
  position: relative;
  padding-bottom: 0.5rem;
  font-weight: 700;
  color: #3b82f6;
}

.section-header h2:after {
  content: '';
  position: absolute;
  bottom: 0;
  right: 0;
  width: 50px;
  height: 3px;
  background-color: #3b82f6;
}

/* Custom button styles */
.btn-gradient {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border: none;
  color: white;
  transition: all 0.3s ease;
}

.btn-gradient:hover {
  background: linear-gradient(135deg, #1d4ed8, #1e40af);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

/* Form styles */
.form-rtl {
  direction: rtl;
  text-align: right;
}

.form-rtl .form-label {
  text-align: right;
}

/* Navigation styles */
.nav-rtl {
  direction: rtl;
}

.nav-rtl .navbar-nav {
  margin-right: auto !important;
  margin-left: 0 !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .ticker-item {
    margin-right: 40px;
  }

  .small-logo {
    width: 25px;
  }

  .hero-section {
    padding: 2rem 0;
  }
}
