import { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router";
import { Save, ArrowRight, X } from "lucide-react";
import { Layout } from "~/components/Layout";
import type { NewsArticle, Classification, Violation, Source, Province } from "~/types";
import {
  MockNewsService as NewsService,
  MockClassificationService as ClassificationService,
  MockViolationService as ViolationService,
  MockSourceService as SourceService,
  MockProvinceService as ProvinceService
} from "~/lib/mockData";

function EditNewsPage() {
  const { id } = useParams();
  const navigate = useNavigate();
  const [news, setNews] = useState<NewsArticle | null>(null);
  const [classifications, setClassifications] = useState<Classification[]>([]);
  const [violations, setViolations] = useState<Violation[]>([]);
  const [sources, setSources] = useState<Source[]>([]);
  const [provinces, setProvinces] = useState<Province[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Form data
  const [formData, setFormData] = useState({
    title: '',
    content: '',
    provinceId: '',
    date: '',
    sourceUrl: '',
    sourceId: '',
    classificationId: '',
    violationId: ''
  });



  useEffect(() => {
    if (id) {
      loadData();
    }
  }, [id]);

  useEffect(() => {
    if (formData.classificationId) {
      loadViolations();
    } else {
      setViolations([]);
    }
  }, [formData.classificationId]);

  const loadData = async () => {
    try {
      setLoading(true);
      const [newsData, classificationsData, sourcesData, provincesData] = await Promise.all([
        NewsService.getById(id!),
        ClassificationService.getAll(),
        SourceService.getAll(),
        ProvinceService.getAll()
      ]);

      if (!newsData) {
        navigate('/news/manage');
        return;
      }

      setNews(newsData);
      setClassifications(classificationsData);
      setSources(sourcesData);
      setProvinces(provincesData);

      // Populate form data
      setFormData({
        title: newsData.title,
        content: newsData.content,
        provinceId: newsData.provinceId,
        date: newsData.date,
        sourceUrl: newsData.sourceUrl,
        sourceId: newsData.sourceId || '',
        classificationId: newsData.classificationId,
        violationId: newsData.violationId
      });

      // Load violations for the selected classification
      if (newsData.classificationId) {
        const violationsData = await ViolationService.getByClassificationId(newsData.classificationId);
        setViolations(violationsData);
      }
    } catch (error) {
      console.error('Error loading data:', error);
      navigate('/news/manage');
    } finally {
      setLoading(false);
    }
  };

  const loadViolations = async () => {
    try {
      const violationsData = await ViolationService.getByClassificationId(formData.classificationId);
      setViolations(violationsData);
    } catch (error) {
      console.error('Error loading violations:', error);
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = 'عنوان الخبر مطلوب';
    }

    if (!formData.content.trim()) {
      newErrors.content = 'محتوى الخبر مطلوب';
    }

    if (!formData.provinceId) {
      newErrors.provinceId = 'المحافظة مطلوبة';
    }

    if (!formData.date) {
      newErrors.date = 'التاريخ مطلوب';
    }

    if (!formData.sourceUrl.trim()) {
      newErrors.sourceUrl = 'رابط المصدر مطلوب';
    }

    if (!formData.classificationId) {
      newErrors.classificationId = 'التصنيف مطلوب';
    }

    if (!formData.violationId) {
      newErrors.violationId = 'الانتهاك مطلوب';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      setSaving(true);

      const updatedNewsData: Partial<NewsArticle> = {
        title: formData.title,
        content: formData.content,
        provinceId: formData.provinceId,
        date: formData.date,
        sourceUrl: formData.sourceUrl,
        sourceId: formData.sourceId || undefined,
        classificationId: formData.classificationId,
        violationId: formData.violationId
      };

      await NewsService.update(id!, updatedNewsData);
      navigate(`/news/${id}`);
    } catch (error) {
      console.error('Error updating news:', error);
      setErrors({ submit: 'حدث خطأ أثناء تحديث الخبر' });
    } finally {
      setSaving(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };



  if (loading) {
    return (
      <Layout>
        <div className="flex justify-center items-center min-h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحميل بيانات الخبر...</p>
          </div>
        </div>
      </Layout>
    );
  }

  if (!news) {
    return (
      <Layout>
        <div className="text-center py-12">
          <h3 className="text-lg font-medium text-gray-900 mb-2">الخبر غير موجود</h3>
          <p className="text-gray-600 mb-6">لم يتم العثور على الخبر المطلوب</p>
          <button
            onClick={() => navigate('/news/manage')}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg inline-flex items-center gap-2"
          >
            <ArrowRight className="h-5 w-5" />
            العودة إلى قائمة الأخبار
          </button>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex items-center gap-4 mb-8">
          <button
            onClick={() => navigate(`/news/${id}`)}
            className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
          >
            <ArrowRight className="h-6 w-6" />
          </button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">تعديل الخبر</h1>
            <p className="text-gray-600 mt-2">
              تعديل معلومات الخبر والبيانات المرتبطة به
            </p>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Basic Information */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">المعلومات الأساسية</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Title */}
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  عنوان الخبر *
                </label>
                <input
                  type="text"
                  value={formData.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.title ? 'border-red-300' : 'border-gray-300'
                  }`}
                  placeholder="أدخل عنوان الخبر"
                />
                {errors.title && (
                  <p className="mt-1 text-sm text-red-600">{errors.title}</p>
                )}
              </div>

              {/* Province */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  المحافظة *
                </label>
                <select
                  value={formData.provinceId}
                  onChange={(e) => handleInputChange('provinceId', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.provinceId ? 'border-red-300' : 'border-gray-300'
                  }`}
                >
                  <option value="">اختر المحافظة</option>
                  {provinces.map(province => (
                    <option key={province.id} value={province.id}>
                      {province.name}
                    </option>
                  ))}
                </select>
                {errors.provinceId && (
                  <p className="mt-1 text-sm text-red-600">{errors.provinceId}</p>
                )}
              </div>

              {/* Date */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  التاريخ *
                </label>
                <input
                  type="date"
                  value={formData.date}
                  onChange={(e) => handleInputChange('date', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.date ? 'border-red-300' : 'border-gray-300'
                  }`}
                />
                {errors.date && (
                  <p className="mt-1 text-sm text-red-600">{errors.date}</p>
                )}
              </div>

              {/* Source URL */}
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  رابط المصدر *
                </label>
                <input
                  type="url"
                  value={formData.sourceUrl}
                  onChange={(e) => handleInputChange('sourceUrl', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.sourceUrl ? 'border-red-300' : 'border-gray-300'
                  }`}
                  placeholder="https://example.com/news-article"
                />
                {errors.sourceUrl && (
                  <p className="mt-1 text-sm text-red-600">{errors.sourceUrl}</p>
                )}
              </div>

              {/* Source (Optional) */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  المصدر (اختياري)
                </label>
                <select
                  value={formData.sourceId}
                  onChange={(e) => handleInputChange('sourceId', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">اختر المصدر</option>
                  {sources.map(source => (
                    <option key={source.id} value={source.id}>
                      {source.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Classification */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  التصنيف *
                </label>
                <select
                  value={formData.classificationId}
                  onChange={(e) => handleInputChange('classificationId', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.classificationId ? 'border-red-300' : 'border-gray-300'
                  }`}
                >
                  <option value="">اختر التصنيف</option>
                  {classifications.map(classification => (
                    <option key={classification.id} value={classification.id}>
                      {classification.name}
                    </option>
                  ))}
                </select>
                {errors.classificationId && (
                  <p className="mt-1 text-sm text-red-600">{errors.classificationId}</p>
                )}
              </div>

              {/* Violation */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  الانتهاك *
                </label>
                <select
                  value={formData.violationId}
                  onChange={(e) => handleInputChange('violationId', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.violationId ? 'border-red-300' : 'border-gray-300'
                  }`}
                  disabled={!formData.classificationId}
                >
                  <option value="">اختر الانتهاك</option>
                  {violations.map(violation => (
                    <option key={violation.id} value={violation.id}>
                      {violation.name}
                    </option>
                  ))}
                </select>
                {errors.violationId && (
                  <p className="mt-1 text-sm text-red-600">{errors.violationId}</p>
                )}
              </div>

              {/* Content */}
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  محتوى الخبر *
                </label>
                <textarea
                  value={formData.content}
                  onChange={(e) => handleInputChange('content', e.target.value)}
                  rows={6}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.content ? 'border-red-300' : 'border-gray-300'
                  }`}
                  placeholder="أدخل محتوى الخبر"
                />
                {errors.content && (
                  <p className="mt-1 text-sm text-red-600">{errors.content}</p>
                )}
              </div>
            </div>
          </div>



          {/* Submit */}
          <div className="flex gap-4">
            <button
              type="button"
              onClick={() => navigate(`/news/${id}`)}
              className="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 py-3 px-6 rounded-lg font-medium transition-colors"
            >
              إلغاء
            </button>
            <button
              type="submit"
              disabled={saving}
              className="flex-1 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white py-3 px-6 rounded-lg font-medium flex items-center justify-center gap-2 transition-colors"
            >
              {saving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  جاري الحفظ...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4" />
                  حفظ التغييرات
                </>
              )}
            </button>
          </div>

          {errors.submit && (
            <div className="bg-red-50 border border-red-200 rounded-md p-4">
              <p className="text-red-600 text-sm">{errors.submit}</p>
            </div>
          )}
        </form>
      </div>
    </Layout>
  );
}

export default EditNewsPage;
