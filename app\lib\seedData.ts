import { 
  NewsService, 
  ClassificationService, 
  SourceService, 
  LegalFileService,
  ClassificationFieldService,
  FieldOptionService,
  ProvinceService 
} from './firestore';

// Iraqi Governorates (المحافظات العراقية)
export const IRAQI_PROVINCES = [
  { name: 'بغد<PERSON>', nameEn: 'Baghdad' },
  { name: 'البصرة', nameEn: '<PERSON><PERSON><PERSON>' },
  { name: 'نينوى', nameEn: 'Nineveh' },
  { name: 'أربي<PERSON>', nameEn: 'Erb<PERSON>' },
  { name: 'النج<PERSON>', nameEn: 'Najaf' },
  { name: 'كربلاء', nameEn: 'Karbala' },
  { name: 'الأنبار', nameEn: 'An<PERSON>' },
  { name: 'بابل', nameEn: 'Babylon' },
  { name: 'ديالى', nameEn: 'Diyala' },
  { name: 'ذي قار', nameEn: 'Dhi Qar' },
  { name: 'المثنى', nameEn: 'Al-<PERSON><PERSON><PERSON>' },
  { name: 'القادسية', nameEn: 'Al-<PERSON><PERSON><PERSON><PERSON><PERSON>' },
  { name: 'صلاح الدين', nameEn: '<PERSON>adin' },
  { name: 'كركوك', nameEn: 'Kirkuk' },
  { name: 'واسط', nameEn: 'Wasit' },
  { name: 'ميسان', nameEn: 'Maysan' },
  { name: 'دهوك', nameEn: 'Dohuk' },
  { name: 'السليمانية', nameEn: 'Sulaymaniyah' }
];

// Sample Legal Files (الملفات الحقوقية)
export const SAMPLE_LEGAL_FILES = [
  {
    title: 'الإعلان العالمي لحقوق الإنسان',
    approvedBy: 'الأمم المتحدة',
    date: '1948-12-10',
    link: 'https://www.un.org/ar/universal-declaration-human-rights/'
  },
  {
    title: 'العهد الدولي الخاص بالحقوق المدنية والسياسية',
    approvedBy: 'الأمم المتحدة',
    date: '1966-12-16',
    link: 'https://www.ohchr.org/ar/instruments-mechanisms/instruments/international-covenant-civil-and-political-rights'
  },
  {
    title: 'اتفاقية حقوق الطفل',
    approvedBy: 'الأمم المتحدة',
    date: '1989-11-20',
    link: 'https://www.unicef.org/ar/child-rights-convention'
  },
  {
    title: 'اتفاقية القضاء على جميع أشكال التمييز ضد المرأة',
    approvedBy: 'الأمم المتحدة',
    date: '1979-12-18',
    link: 'https://www.un.org/womenwatch/daw/cedaw/text/0360793A.pdf'
  },
  {
    title: 'الدستور العراقي',
    approvedBy: 'الحكومة العراقية',
    date: '2005-10-15',
    link: 'https://www.constituteproject.org/constitution/Iraq_2005.pdf'
  }
];

// Sample Sources (المصادر)
export const SAMPLE_SOURCES = [
  { name: 'وكالة الأنباء العراقية', type: 'website' as const, url: 'https://ina.iq' },
  { name: 'قناة العراقية', type: 'tv' as const, url: 'https://iraqtv.iq' },
  { name: 'صحيفة الصباح', type: 'newspaper' as const, url: 'https://alsabaah.iq' },
  { name: 'شبكة الإعلام العراقي', type: 'tv' as const, url: 'https://imn.iq' },
  { name: 'وكالة شفق نيوز', type: 'website' as const, url: 'https://shafaq.com' },
  { name: 'قناة السومرية', type: 'tv' as const, url: 'https://alsumaria.tv' },
  { name: 'موقع فيسبوك', type: 'social' as const, url: 'https://facebook.com' },
  { name: 'موقع تويتر', type: 'social' as const, url: 'https://twitter.com' }
];

// Sample Classifications with Dynamic Fields
export const SAMPLE_CLASSIFICATIONS = [
  {
    name: 'انتهاكات حقوق الإنسان',
    description: 'تصنيف للأخبار المتعلقة بانتهاكات حقوق الإنسان العامة',
    color: '#ef4444',
    icon: 'AlertTriangle',
    fields: [
      {
        label: 'نوع الانتهاك',
        type: 'select' as const,
        required: true,
        order: 1,
        options: ['اعتقال تعسفي', 'تعذيب', 'قتل خارج القانون', 'اختفاء قسري', 'انتهاك حرية التعبير']
      },
      {
        label: 'عدد الضحايا',
        type: 'number' as const,
        required: true,
        order: 2,
        validation: { min: 1 }
      },
      {
        label: 'الجهة المسؤولة',
        type: 'text' as const,
        required: false,
        order: 3
      },
      {
        label: 'تاريخ الحادثة',
        type: 'date' as const,
        required: true,
        order: 4
      }
    ]
  },
  {
    name: 'حقوق المرأة',
    description: 'تصنيف للأخبار المتعلقة بحقوق المرأة والمساواة الجنسية',
    color: '#8b5cf6',
    icon: 'Users',
    fields: [
      {
        label: 'نوع الانتهاك',
        type: 'select' as const,
        required: true,
        order: 1,
        options: ['تمييز في العمل', 'عنف أسري', 'منع من التعليم', 'تمييز قانوني', 'تحرش']
      },
      {
        label: 'العمر',
        type: 'number' as const,
        required: false,
        order: 2,
        validation: { min: 1, max: 100 }
      },
      {
        label: 'الحالة الاجتماعية',
        type: 'select' as const,
        required: false,
        order: 3,
        options: ['عزباء', 'متزوجة', 'مطلقة', 'أرملة']
      }
    ]
  },
  {
    name: 'حقوق الطفل',
    description: 'تصنيف للأخبار المتعلقة بحقوق الطفل وحمايته',
    color: '#06b6d4',
    icon: 'Baby',
    fields: [
      {
        label: 'نوع الانتهاك',
        type: 'select' as const,
        required: true,
        order: 1,
        options: ['عمالة الأطفال', 'تجنيد الأطفال', 'عنف ضد الأطفال', 'منع من التعليم', 'إهمال']
      },
      {
        label: 'عمر الطفل',
        type: 'number' as const,
        required: true,
        order: 2,
        validation: { min: 0, max: 18 }
      },
      {
        label: 'الجنس',
        type: 'select' as const,
        required: false,
        order: 3,
        options: ['ذكر', 'أنثى']
      },
      {
        label: 'هل تم الإبلاغ للسلطات؟',
        type: 'boolean' as const,
        required: false,
        order: 4
      }
    ]
  },
  {
    name: 'الحريات العامة',
    description: 'تصنيف للأخبار المتعلقة بالحريات العامة وحرية التعبير',
    color: '#10b981',
    icon: 'Megaphone',
    fields: [
      {
        label: 'نوع الحرية المنتهكة',
        type: 'select' as const,
        required: true,
        order: 1,
        options: ['حرية التعبير', 'حرية التجمع', 'حرية الصحافة', 'حرية الرأي', 'حرية التنقل']
      },
      {
        label: 'مكان الحادثة',
        type: 'text' as const,
        required: false,
        order: 2
      },
      {
        label: 'تفاصيل إضافية',
        type: 'textarea' as const,
        required: false,
        order: 3
      }
    ]
  }
];

// Seed Database Function
export async function seedDatabase() {
  try {
    console.log('🌱 بدء إنشاء البيانات الأولية...');

    // 1. Create Legal Files
    console.log('📄 إنشاء الملفات الحقوقية...');
    const legalFileIds: Record<string, string> = {};
    for (const legalFile of SAMPLE_LEGAL_FILES) {
      const id = await LegalFileService.create(legalFile);
      legalFileIds[legalFile.title] = id;
    }

    // 2. Create Sources
    console.log('📺 إنشاء المصادر...');
    const sourceIds: string[] = [];
    for (const source of SAMPLE_SOURCES) {
      const id = await SourceService.create({
        ...source,
        isActive: true
      });
      sourceIds.push(id);
    }

    // 3. Create Provinces
    console.log('🗺️ إنشاء المحافظات...');
    for (const province of IRAQI_PROVINCES) {
      await ProvinceService.create({
        ...province,
        isActive: true
      });
    }

    // 4. Create Classifications with Dynamic Fields
    console.log('🏷️ إنشاء التصنيفات والحقول الديناميكية...');
    for (let i = 0; i < SAMPLE_CLASSIFICATIONS.length; i++) {
      const classification = SAMPLE_CLASSIFICATIONS[i];
      const legalFileId = Object.values(legalFileIds)[i] || Object.values(legalFileIds)[0];
      
      const classificationId = await ClassificationService.create({
        name: classification.name,
        description: classification.description,
        color: classification.color,
        icon: classification.icon,
        legalFileId,
        isActive: true
      });

      // Create dynamic fields for this classification
      for (const field of classification.fields) {
        const fieldId = await ClassificationFieldService.create({
          classificationId,
          label: field.label,
          type: field.type,
          required: field.required,
          order: field.order,
          validation: field.validation
        });

        // Create options for select fields
        if (field.type === 'select' && field.options) {
          for (let j = 0; j < field.options.length; j++) {
            await FieldOptionService.create({
              fieldId,
              value: field.options[j],
              order: j + 1
            });
          }
        }
      }
    }

    console.log('✅ تم إنشاء البيانات الأولية بنجاح!');
    return true;
  } catch (error) {
    console.error('❌ خطأ في إنشاء البيانات الأولية:', error);
    throw error;
  }
}
