import { useState } from "react";
import { 
  X, 
  Save, 
  Type,
  Hash,
  Calendar,
  List,
  FileText,
  ToggleLeft,
  Plus,
  Trash2
} from "lucide-react";
import type { ClassificationField } from "~/types";
import { 
  MockClassificationFieldService as ClassificationFieldService,
  MockFieldOptionService as FieldOptionService 
} from "~/lib/mockData";

interface AddFieldModalProps {
  classificationId: string;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const fieldTypes = [
  { value: 'text', label: 'نص قصير', icon: Type, description: 'حقل نص عادي' },
  { value: 'textarea', label: 'نص طويل', icon: FileText, description: 'منطقة نص متعددة الأسطر' },
  { value: 'number', label: 'رقم', icon: Hash, description: 'حقل رقمي' },
  { value: 'date', label: 'تاريخ', icon: Calendar, description: 'حقل تاريخ' },
  { value: 'select', label: 'قائمة منسدلة', icon: List, description: 'اختيار من قائمة' },
  { value: 'boolean', label: 'صح/خطأ', icon: ToggleLeft, description: 'حقل منطقي' }
];

export default function AddFieldModal({ classificationId, isOpen, onClose, onSuccess }: AddFieldModalProps) {
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [options, setOptions] = useState<string[]>(['']);
  
  const [formData, setFormData] = useState({
    label: '',
    type: 'text',
    required: false,
    order: 1,
    validation: {}
  });

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.label.trim()) {
      newErrors.label = 'تسمية الحقل مطلوبة';
    }

    if (formData.type === 'select' && options.filter(opt => opt.trim()).length === 0) {
      newErrors.options = 'يجب إضافة خيار واحد على الأقل للقائمة المنسدلة';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setLoading(true);
    try {
      // Create the field
      const fieldId = await ClassificationFieldService.create({
        ...formData,
        classificationId
      });

      // Create options for select fields
      if (formData.type === 'select') {
        const validOptions = options.filter(opt => opt.trim());
        for (let i = 0; i < validOptions.length; i++) {
          await FieldOptionService.create({
            fieldId,
            value: validOptions[i].trim(),
            order: i + 1
          });
        }
      }

      onSuccess();
      onClose();
      resetForm();
    } catch (error) {
      console.error('Error creating field:', error);
      setErrors({ submit: 'حدث خطأ أثناء إنشاء الحقل' });
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      label: '',
      type: 'text',
      required: false,
      order: 1,
      validation: {}
    });
    setOptions(['']);
    setErrors({});
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const addOption = () => {
    setOptions(prev => [...prev, '']);
  };

  const updateOption = (index: number, value: string) => {
    setOptions(prev => prev.map((opt, i) => i === index ? value : opt));
    if (errors.options) {
      setErrors(prev => ({ ...prev, options: '' }));
    }
  };

  const removeOption = (index: number) => {
    setOptions(prev => prev.filter((_, i) => i !== index));
  };

  if (!isOpen) return null;

  const selectedFieldType = fieldTypes.find(ft => ft.value === formData.type);
  const SelectedIcon = selectedFieldType?.icon || Type;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">إضافة حقل جديد</h3>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Field Label */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              تسمية الحقل *
            </label>
            <input
              type="text"
              value={formData.label}
              onChange={(e) => handleInputChange('label', e.target.value)}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errors.label ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="مثال: نوع الانتهاك"
            />
            {errors.label && (
              <p className="text-red-500 text-sm mt-1">{errors.label}</p>
            )}
          </div>

          {/* Field Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              نوع الحقل *
            </label>
            <div className="grid grid-cols-2 gap-3">
              {fieldTypes.map(({ value, label, icon: IconComponent, description }) => (
                <button
                  key={value}
                  type="button"
                  onClick={() => handleInputChange('type', value)}
                  className={`p-4 border-2 rounded-lg text-right transition-all ${
                    formData.type === value
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-center gap-3">
                    <IconComponent className="h-5 w-5 text-gray-500" />
                    <div>
                      <p className="font-medium text-gray-900">{label}</p>
                      <p className="text-xs text-gray-500">{description}</p>
                    </div>
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* Options for Select Fields */}
          {formData.type === 'select' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                خيارات القائمة المنسدلة *
              </label>
              <div className="space-y-3">
                {options.map((option, index) => (
                  <div key={index} className="flex gap-2">
                    <input
                      type="text"
                      value={option}
                      onChange={(e) => updateOption(index, e.target.value)}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder={`الخيار ${index + 1}`}
                    />
                    {options.length > 1 && (
                      <button
                        type="button"
                        onClick={() => removeOption(index)}
                        className="p-2 text-red-500 hover:text-red-700 transition-colors"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    )}
                  </div>
                ))}
                <button
                  type="button"
                  onClick={addOption}
                  className="flex items-center gap-2 text-blue-600 hover:text-blue-700 text-sm"
                >
                  <Plus className="h-4 w-4" />
                  إضافة خيار جديد
                </button>
              </div>
              {errors.options && (
                <p className="text-red-500 text-sm mt-1">{errors.options}</p>
              )}
            </div>
          )}

          {/* Settings */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                ترتيب الحقل
              </label>
              <input
                type="number"
                value={formData.order}
                onChange={(e) => handleInputChange('order', parseInt(e.target.value) || 1)}
                min="1"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <div className="flex items-end">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.required}
                  onChange={(e) => handleInputChange('required', e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="mr-3 text-sm text-gray-700">
                  حقل مطلوب
                </span>
              </label>
            </div>
          </div>

          {/* Preview */}
          <div className="bg-gray-50 rounded-lg p-4">
            <p className="text-sm font-medium text-gray-700 mb-3">معاينة الحقل:</p>
            <div className="flex items-center gap-3">
              <SelectedIcon className="h-5 w-5 text-gray-500" />
              <span className="font-medium">
                {formData.label || 'تسمية الحقل'}
              </span>
              {formData.required && (
                <span className="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">
                  مطلوب
                </span>
              )}
              <span className="bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded-full">
                {selectedFieldType?.label}
              </span>
            </div>
          </div>

          {/* Submit Buttons */}
          <div className="flex gap-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 py-3 px-4 rounded-lg font-medium transition-colors"
            >
              إلغاء
            </button>
            <button
              type="submit"
              disabled={loading}
              className="flex-1 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white py-3 px-4 rounded-lg font-medium flex items-center justify-center gap-2 transition-colors"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  جاري الحفظ...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4" />
                  حفظ الحقل
                </>
              )}
            </button>
          </div>

          {errors.submit && (
            <div className="bg-red-50 border border-red-200 rounded-md p-4">
              <p className="text-red-700 text-sm">{errors.submit}</p>
            </div>
          )}
        </form>
      </div>
    </div>
  );
}
