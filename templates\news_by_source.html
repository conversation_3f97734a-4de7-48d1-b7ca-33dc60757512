{% extends 'layout.html' %}

{% block title %}أخبار من مصدر: {{ source_name }}{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- شريط التنقل -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('index') }}">الرئيسية</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('statistics') }}">الإحصائيات</a></li>
            <li class="breadcrumb-item active" aria-current="page">أخبار من مصدر: {{ source_name }}</li>
        </ol>
    </nav>

    <!-- عنوان الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-1">
                <i class="fas fa-rss text-primary me-2"></i>
                أخبار من مصدر: {{ source_name }}
            </h2>
            <p class="text-muted mb-0">
                <i class="fas fa-newspaper me-1"></i>
                إجمالي {{ news_list|length }} خبر
            </p>
        </div>
        <div>
            <a href="{{ url_for('statistics') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i> العودة للإحصائيات
            </a>
        </div>
    </div>

    {% if news_list %}
        <!-- قائمة الأخبار -->
        <div class="row">
            {% for news in news_list %}
            <div class="col-12 mb-3">
                <div class="card shadow-sm h-100">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <h5 class="card-title">
                                    <a href="{{ url_for('view_news_details', news_id=news.id) }}" 
                                       class="text-decoration-none">
                                        {{ news.title }}
                                    </a>
                                </h5>
                                <p class="card-text text-muted">
                                    {{ news.content[:200] }}{% if news.content|length > 200 %}...{% endif %}
                                </p>
                                
                                <!-- معلومات إضافية -->
                                <div class="row text-muted small">
                                    <div class="col-md-4">
                                        <i class="fas fa-map-marker-alt me-1"></i>
                                        {{ news.governorate.name }}
                                    </div>
                                    <div class="col-md-4">
                                        <i class="fas fa-tag me-1"></i>
                                        {{ news.category.name }}
                                    </div>
                                    <div class="col-md-4">
                                        <i class="fas fa-calendar me-1"></i>
                                        {{ news.date.strftime('%Y-%m-%d') }}
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-4 text-end">
                                <div class="mb-2">
                                    <span class="badge bg-primary">{{ news.category.name }}</span>
                                </div>
                                
                                <div class="mb-2">
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>
                                        {% set time_diff = (now - news.created_at).total_seconds() %}
                                        {% if time_diff < 3600 %}
                                            منذ {{ (time_diff / 60)|round|int }} دقيقة
                                        {% elif time_diff < 86400 %}
                                            منذ {{ (time_diff / 3600)|round|int }} ساعة
                                        {% else %}
                                            منذ {{ (time_diff / 86400)|round|int }} يوم
                                        {% endif %}
                                    </small>
                                </div>
                                
                                <div class="btn-group" role="group">
                                    <a href="{{ url_for('view_news_details', news_id=news.id) }}" 
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i> عرض
                                    </a>
                                    {% if news.source_url %}
                                    <a href="{{ news.source_url }}" target="_blank" 
                                       class="btn btn-sm btn-outline-info">
                                        <i class="fas fa-external-link-alt"></i> المصدر
                                    </a>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        
        <!-- إحصائيات سريعة -->
        <div class="row mt-4">
            <div class="col-md-12">
                <div class="card bg-light">
                    <div class="card-body">
                        <h6 class="card-title">
                            <i class="fas fa-chart-bar me-2"></i>
                            إحصائيات سريعة لمصدر: {{ source_name }}
                        </h6>
                        <div class="row text-center">
                            <div class="col-md-3">
                                <div class="border-end">
                                    <h4 class="text-primary mb-1">{{ news_list|length }}</h4>
                                    <small class="text-muted">إجمالي الأخبار</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="border-end">
                                    <h4 class="text-success mb-1">{{ news_list|selectattr('category')|map(attribute='category.name')|unique|list|length }}</h4>
                                    <small class="text-muted">التصنيفات</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="border-end">
                                    <h4 class="text-warning mb-1">{{ news_list|selectattr('governorate')|map(attribute='governorate.name')|unique|list|length }}</h4>
                                    <small class="text-muted">المحافظات</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <h4 class="text-info mb-1">
                                    {% if news_list %}
                                        {{ news_list[0].date.strftime('%Y-%m-%d') }}
                                    {% else %}
                                        -
                                    {% endif %}
                                </h4>
                                <small class="text-muted">آخر خبر</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
    {% else %}
        <!-- رسالة عدم وجود أخبار -->
        <div class="text-center py-5">
            <div class="mb-4">
                <i class="fas fa-newspaper fa-5x text-muted"></i>
            </div>
            <h4 class="text-muted">لا توجد أخبار من هذا المصدر</h4>
            <p class="text-muted">لم يتم العثور على أي أخبار من مصدر "{{ source_name }}"</p>
            <a href="{{ url_for('statistics') }}" class="btn btn-primary">
                <i class="fas fa-arrow-right me-1"></i> العودة للإحصائيات
            </a>
        </div>
    {% endif %}
</div>
{% endblock %}
