import { useState } from "react";
import { Layout } from "~/components/Layout";
import { Database, CheckCircle, AlertCircle, Loader } from "lucide-react";
import { initializeMockData } from "~/lib/mockData";
import type { Route } from "./+types/index";

export function meta({}: Route.MetaArgs) {
  return [
    { title: "الإعدادات - نظام الرصد الإعلامي" },
  ];
}

export default function SettingsIndex() {
  const [isSeeding, setIsSeeding] = useState(false);
  const [seedResult, setSeedResult] = useState<{ success: boolean; message: string } | null>(null);

  const handleSeedDatabase = async () => {
    setIsSeeding(true);
    setSeedResult(null);

    try {
      // Simulate loading time
      await new Promise(resolve => setTimeout(resolve, 1000));
      initializeMockData();
      setSeedResult({
        success: true,
        message: "تم تحميل البيانات التجريبية بنجاح! النظام جاهز للاستخدام في وضع التطوير."
      });
    } catch (error) {
      setSeedResult({
        success: false,
        message: `حدث خطأ أثناء تحميل البيانات: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`
      });
    } finally {
      setIsSeeding(false);
    }
  };

  return (
    <Layout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">الإعدادات</h1>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Database Setup Section */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center mb-4">
              <Database className="h-6 w-6 text-blue-600 ml-3" />
              <h2 className="text-xl font-semibold text-gray-900">البيانات التجريبية</h2>
            </div>

            <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-4">
              <p className="text-yellow-800 text-sm">
                <strong>وضع التطوير:</strong> النظام يعمل حالياً بالبيانات التجريبية المحلية.
                لا حاجة لإعداد Firebase في هذه المرحلة.
              </p>
            </div>

            <p className="text-gray-600 mb-6">
              تحميل البيانات التجريبية للنظام بما في ذلك المحافظات العراقية، المصادر الإعلامية،
              التصنيفات، والملفات الحقوقية مع الحقول الديناميكية.
            </p>

            <div className="space-y-4">
              <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                <h3 className="font-medium text-blue-900 mb-2">البيانات التجريبية المتاحة:</h3>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• 6 محافظات عراقية رئيسية</li>
                  <li>• 4 مصادر إعلامية متنوعة</li>
                  <li>• 3 ملفات حقوقية أساسية</li>
                  <li>• 3 تصنيفات مع حقول ديناميكية</li>
                </ul>
              </div>

              {seedResult && (
                <div className={`border rounded-md p-4 ${
                  seedResult.success
                    ? 'bg-green-50 border-green-200'
                    : 'bg-red-50 border-red-200'
                }`}>
                  <div className="flex items-center">
                    {seedResult.success ? (
                      <CheckCircle className="h-5 w-5 text-green-600 ml-2" />
                    ) : (
                      <AlertCircle className="h-5 w-5 text-red-600 ml-2" />
                    )}
                    <p className={`text-sm ${
                      seedResult.success ? 'text-green-800' : 'text-red-800'
                    }`}>
                      {seedResult.message}
                    </p>
                  </div>
                </div>
              )}

              <button
                onClick={handleSeedDatabase}
                disabled={isSeeding}
                className="w-full inline-flex items-center justify-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {isSeeding ? (
                  <>
                    <Loader className="h-4 w-4 ml-2 animate-spin" />
                    جاري إنشاء البيانات...
                  </>
                ) : (
                  <>
                    <Database className="h-4 w-4 ml-2" />
                    تحميل البيانات التجريبية
                  </>
                )}
              </button>
            </div>
          </div>

          {/* System Information */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">معلومات النظام</h2>

            <div className="space-y-4">
              <div className="border-b border-gray-200 pb-4">
                <h3 className="font-medium text-gray-900">الإصدار</h3>
                <p className="text-gray-600">1.0.0</p>
              </div>

              <div className="border-b border-gray-200 pb-4">
                <h3 className="font-medium text-gray-900">قاعدة البيانات</h3>
                <p className="text-gray-600">Firebase Firestore</p>
              </div>

              <div className="border-b border-gray-200 pb-4">
                <h3 className="font-medium text-gray-900">المصادقة</h3>
                <p className="text-gray-600">Firebase Authentication</p>
              </div>

              <div>
                <h3 className="font-medium text-gray-900">التخزين</h3>
                <p className="text-gray-600">Firebase Storage</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}
