import type {
  NewsArticle,
  MainCategory,
  Source,
  Violation,
  DynamicField,
  FieldValue,
  Province,
  User,
  SearchFilters,
  SearchResult
} from "~/types";

// Mock Storage
let mockNews: NewsArticle[] = [];
let mockMainCategories: MainCategory[] = [];
let mockSources: Source[] = [];
let mockViolations: Violation[] = [];
let mockDynamicFields: DynamicField[] = [];
let mockFieldValues: FieldValue[] = [];
let mockProvinces: Province[] = [];
let mockUsers: User[] = [];

// Helper function to generate IDs
const generateId = () => Math.random().toString(36).substr(2, 9);

// Initialize with sample data
export function initializeMockData() {
  // Users (المستخدمين)
  mockUsers = [
    {
      id: 'user1',
      username: 'admin',
      email: '<EMAIL>',
      password: 'hashed_password_123',
      role: 'admin',
      createdAt: new Date('2024-01-01'),
      lastLoginAt: new Date()
    },
    {
      id: 'user2',
      username: 'editor1',
      email: '<EMAIL>',
      password: 'hashed_password_456',
      role: 'editor',
      createdAt: new Date('2024-01-02'),
      lastLoginAt: new Date()
    }
  ];

  // Provinces (المحافظات)
  mockProvinces = [
    { id: 'prov1', name: 'بغداد' },
    { id: 'prov2', name: 'البصرة' },
    { id: 'prov3', name: 'الموصل' },
    { id: 'prov4', name: 'أربيل' },
    { id: 'prov5', name: 'النجف' },
    { id: 'prov6', name: 'كربلاء' },
    { id: 'prov7', name: 'الأنبار' },
    { id: 'prov8', name: 'ديالى' },
    { id: 'prov9', name: 'كركوك' },
    { id: 'prov10', name: 'بابل' },
    { id: 'prov11', name: 'واسط' },
    { id: 'prov12', name: 'صلاح الدين' },
    { id: 'prov13', name: 'القادسية' },
    { id: 'prov14', name: 'المثنى' },
    { id: 'prov15', name: 'ذي قار' },
    { id: 'prov16', name: 'ميسان' },
    { id: 'prov17', name: 'دهوك' },
    { id: 'prov18', name: 'السليمانية' }
  ];

  // Main Categories (التصنيفات الرئيسية - الحقوق الأساسية)
  mockMainCategories = [
    {
      id: 'cat1',
      name: 'الحق في الحياة والأمان الشخصي',
      description: 'الحق الأساسي في الحياة وعدم التعرض للقتل أو التهديد',
      isActive: true
    },
    {
      id: 'cat2',
      name: 'حقوق المرأة',
      description: 'الحقوق المتعلقة بالمساواة بين الجنسين ومنع التمييز ضد المرأة',
      isActive: true
    },
    {
      id: 'cat3',
      name: 'حقوق الطفل',
      description: 'الحقوق الخاصة بحماية الأطفال ورعايتهم وتعليمهم',
      isActive: true
    },
    {
      id: 'cat4',
      name: 'الحق في التعليم',
      description: 'الحق في الحصول على التعليم المجاني والجودة',
      isActive: true
    },
    {
      id: 'cat5',
      name: 'الحق في الصحة',
      description: 'الحق في الحصول على الرعاية الصحية والخدمات الطبية',
      isActive: true
    },
    {
      id: 'cat6',
      name: 'حرية التعبير والرأي',
      description: 'الحق في التعبير عن الآراء والأفكار بحرية',
      isActive: true
    },
    {
      id: 'cat7',
      name: 'الحق في العمل',
      description: 'الحق في العمل والحصول على أجر عادل',
      isActive: true
    },
    {
      id: 'cat8',
      name: 'حقوق الأشخاص ذوي الإعاقة',
      description: 'الحقوق الخاصة بالأشخاص ذوي الإعاقة وإدماجهم في المجتمع',
      isActive: true
    }
  ];

  // Sources (المصادر)
  mockSources = [
    {
      id: 'source1',
      name: 'وكالة الأنباء العراقية',
      description: 'الوكالة الرسمية للأنباء في العراق',
      contactInfo: '<EMAIL>'
    },
    {
      id: 'source2',
      name: 'شبكة الإعلام العراقي',
      description: 'شبكة إعلامية عراقية',
      contactInfo: '<EMAIL>'
    },
    {
      id: 'source3',
      name: 'منظمة حقوق الإنسان العراقية',
      description: 'منظمة مجتمع مدني تعنى بحقوق الإنسان',
      contactInfo: '<EMAIL>'
    }
  ];

  // Violations (الانتهاكات الفرعية)
  mockViolations = [
    // انتهاكات الحق في الحياة والأمان الشخصي
    {
      id: 'viol1',
      mainCategoryId: 'cat1',
      name: 'القتل العمد',
      description: 'القتل المتعمد للأشخاص',
      relatedRight: 'الحق في الحياة',
      isActive: true
    },
    {
      id: 'viol2',
      mainCategoryId: 'cat1',
      name: 'التهديد بالقتل',
      description: 'تهديد الأشخاص بالقتل أو الإيذاء',
      relatedRight: 'الحق في الأمان الشخصي',
      isActive: true
    },
    // انتهاكات حقوق المرأة
    {
      id: 'viol3',
      mainCategoryId: 'cat2',
      name: 'التمييز في العمل',
      description: 'التمييز ضد المرأة في مكان العمل',
      relatedRight: 'الحق في المساواة في العمل',
      isActive: true
    },
    {
      id: 'viol4',
      mainCategoryId: 'cat2',
      name: 'العنف الأسري',
      description: 'العنف الممارس ضد المرأة في المنزل',
      relatedRight: 'الحق في الحماية من العنف',
      isActive: true
    },
    // انتهاكات حقوق الطفل
    {
      id: 'viol5',
      mainCategoryId: 'cat3',
      name: 'عمالة الأطفال',
      description: 'استغلال الأطفال في العمل',
      relatedRight: 'الحق في الحماية من الاستغلال',
      isActive: true
    },
    {
      id: 'viol6',
      mainCategoryId: 'cat3',
      name: 'منع الطفل من التعليم',
      description: 'حرمان الطفل من حقه في التعليم',
      relatedRight: 'الحق في التعليم',
      isActive: true
    }
  ];

  // Dynamic Fields (الحقول الديناميكية)
  mockDynamicFields = [
    // حقول القتل العمد
    {
      id: 'field1',
      violationId: 'viol1',
      name: 'victim_name',
      label: 'اسم الضحية',
      type: 'text',
      isRequired: true,
      placeholder: 'أدخل اسم الضحية',
      order: 1
    },
    {
      id: 'field2',
      violationId: 'viol1',
      name: 'victim_age',
      label: 'عمر الضحية',
      type: 'number',
      isRequired: false,
      placeholder: 'أدخل عمر الضحية',
      order: 2
    },
    {
      id: 'field3',
      violationId: 'viol1',
      name: 'weapon_type',
      label: 'نوع السلاح المستخدم',
      type: 'select',
      isRequired: false,
      options: ['سلاح ناري', 'سلاح أبيض', 'متفجرات', 'أخرى'],
      order: 3
    },
    // حقول التمييز في العمل
    {
      id: 'field4',
      violationId: 'viol3',
      name: 'workplace_name',
      label: 'اسم مكان العمل',
      type: 'text',
      isRequired: true,
      placeholder: 'أدخل اسم الشركة أو المؤسسة',
      order: 1
    },
    {
      id: 'field5',
      violationId: 'viol3',
      name: 'discrimination_type',
      label: 'نوع التمييز',
      type: 'select',
      isRequired: true,
      options: ['راتب أقل', 'منع من الترقية', 'فصل تعسفي', 'تحرش', 'أخرى'],
      order: 2
    },
    {
      id: 'field6',
      violationId: 'viol3',
      name: 'affected_women_count',
      label: 'عدد النساء المتضررات',
      type: 'number',
      isRequired: false,
      placeholder: 'أدخل العدد',
      order: 3
    }
  ];
  mockClassifications = [
    {
      id: 'class1',
      legalFileId: 'legal1',
      name: 'الحقوق المدنية والسياسية',
      description: 'انتهاكات تتعلق بالحقوق المدنية والسياسية للمواطنين'
    },
    {
      id: 'class2',
      legalFileId: 'legal2',
      name: 'حقوق المرأة',
      description: 'انتهاكات تتعلق بحقوق المرأة والمساواة بين الجنسين'
    },
    {
      id: 'class3',
      legalFileId: 'legal3',
      name: 'حقوق الطفل',
      description: 'انتهاكات تتعلق بحقوق الأطفال وحمايتهم'
    }
  ];

  // Violations (الانتهاكات)
  mockViolations = [
    {
      id: 'viol1',
      classificationId: 'class1',
      name: 'الاعتقال التعسفي',
      relatedRight: 'الحق في الحرية والأمان الشخصي'
    },
    {
      id: 'viol2',
      classificationId: 'class1',
      name: 'التعذيب',
      relatedRight: 'الحق في عدم التعرض للتعذيب'
    },
    {
      id: 'viol3',
      classificationId: 'class2',
      name: 'التمييز ضد المرأة',
      relatedRight: 'الحق في المساواة وعدم التمييز'
    },
    {
      id: 'viol4',
      classificationId: 'class3',
      name: 'عمالة الأطفال',
      relatedRight: 'حق الطفل في الحماية من الاستغلال الاقتصادي'
    }
  ];

  // Violation Fields (الحقول الديناميكية للانتهاكات)
  mockViolationFields = [
    {
      id: 'field1',
      violationId: 'viol1',
      name: 'عدد المعتقلين',
      type: 'number',
      required: true,
      canAddNewOptions: false
    },
    {
      id: 'field2',
      violationId: 'viol1',
      name: 'مكان الاعتقال',
      type: 'text',
      required: true,
      canAddNewOptions: false
    },
    {
      id: 'field3',
      violationId: 'viol2',
      name: 'نوع التعذيب',
      type: 'select_text',
      required: true,
      canAddNewOptions: true
    },
    {
      id: 'field4',
      violationId: 'viol3',
      name: 'نوع التمييز',
      type: 'select_text',
      required: true,
      canAddNewOptions: true
    }
  ];

  // Select Options (خيارات القوائم المنسدلة)
  mockSelectOptions = [
    {
      id: 'opt1',
      fieldId: 'field3',
      textValue: 'تعذيب جسدي'
    },
    {
      id: 'opt2',
      fieldId: 'field3',
      textValue: 'تعذيب نفسي'
    },
    {
      id: 'opt3',
      fieldId: 'field4',
      textValue: 'تمييز في العمل'
    },
    {
      id: 'opt4',
      fieldId: 'field4',
      textValue: 'تمييز في التعليم'
    }
  ];

  // Sample News (أخبار تجريبية)
  mockNews = [
    {
      id: 'news1',
      title: 'حادثة قتل في بغداد',
      content: 'تم رصد حادثة قتل عمد في العاصمة بغداد، حيث تعرض المواطن أحمد محمد للقتل بسلاح ناري...',
      provinceId: 'prov1',
      date: '2024-01-15',
      sourceUrl: 'https://example.com/news1',
      sourceId: 'source1',
      mainCategoryId: 'cat1',
      violationId: 'viol1',
      createdBy: 'user1'
    },
    {
      id: 'news2',
      title: 'تمييز ضد المرأة في مكان العمل بالبصرة',
      content: 'رصدت منظمات حقوق الإنسان حالة تمييز ضد النساء في إحدى الشركات بالبصرة، حيث تم منع النساء من الترقية...',
      provinceId: 'prov2',
      date: '2024-01-14',
      sourceUrl: 'https://example.com/news2',
      sourceId: 'source2',
      mainCategoryId: 'cat2',
      violationId: 'viol3',
      createdBy: 'user1'
    },
    {
      id: 'news3',
      title: 'استغلال الأطفال في العمل بالموصل',
      content: 'تم اكتشاف حالات استغلال للأطفال في العمل بمدينة الموصل، حيث يعمل أطفال دون سن 15 عاماً في ظروف صعبة...',
      provinceId: 'prov3',
      date: '2024-01-13',
      sourceUrl: 'https://example.com/news3',
      sourceId: 'source3',
      mainCategoryId: 'cat3',
      violationId: 'viol5',
      createdBy: 'user2'
    }
  ];

  // Sample Field Values (قيم الحقول التجريبية)
  mockFieldValuesText = [
    {
      id: 'val1',
      newsId: 'news1',
      fieldId: 'field2',
      textValue: 'مركز شرطة الكرادة'
    }
  ];

  mockFieldValuesNumber = [
    {
      id: 'val2',
      newsId: 'news1',
      fieldId: 'field1',
      numberValue: 5
    }
  ];

  mockFieldValuesSelect = [
    {
      id: 'val3',
      newsId: 'news2',
      fieldId: 'field4',
      optionId: 'opt3'
    }
  ];

  console.log('✅ تم تحميل البيانات التجريبية الجديدة بنجاح');
}

// Mock Services
export class MockNewsService {
  static async getAll(filters?: SearchFilters): Promise<SearchResult<NewsArticle>> {
    let filteredNews = [...mockNews];

    if (filters?.classificationId) {
      filteredNews = filteredNews.filter(news => news.mainCategoryId === filters.classificationId);
    }

    if (filters?.violationId) {
      filteredNews = filteredNews.filter(news => news.violationId === filters.violationId);
    }

    if (filters?.provinceId) {
      filteredNews = filteredNews.filter(news => news.provinceId === filters.provinceId);
    }

    if (filters?.sourceId) {
      filteredNews = filteredNews.filter(news => news.sourceId === filters.sourceId);
    }

    if (filters?.searchText) {
      const searchLower = filters.searchText.toLowerCase();
      filteredNews = filteredNews.filter(news =>
        news.title.toLowerCase().includes(searchLower) ||
        news.content.toLowerCase().includes(searchLower)
      );
    }

    return {
      items: filteredNews,
      total: filteredNews.length,
      page: 1,
      limit: 10,
      totalPages: Math.ceil(filteredNews.length / 10)
    };
  }

  static async getById(id: string): Promise<NewsArticle | null> {
    return mockNews.find(news => news.id === id) || null;
  }

  static async create(newsData: Omit<NewsArticle, 'id'>): Promise<string> {
    const id = generateId();
    const newNews: NewsArticle = {
      ...newsData,
      id
    };
    mockNews.push(newNews);
    console.log('✅ تم حفظ الخبر:', newNews.title);
    return id;
  }

  static async update(id: string, updates: Partial<NewsArticle>): Promise<void> {
    const index = mockNews.findIndex(news => news.id === id);
    if (index !== -1) {
      mockNews[index] = { ...mockNews[index], ...updates };
    }
  }

  static async delete(id: string): Promise<void> {
    mockNews = mockNews.filter(news => news.id !== id);
  }
}

export class MockMainCategoryService {
  static async getAll(): Promise<MainCategory[]> {
    console.log('MockMainCategoryService.getAll called, data:', mockMainCategories);
    return [...mockMainCategories];
  }

  static async getById(id: string): Promise<MainCategory | null> {
    return mockMainCategories.find(c => c.id === id) || null;
  }

  static async create(data: Omit<MainCategory, 'id'>): Promise<string> {
    const id = generateId();
    const newCategory: MainCategory = { ...data, id };
    mockMainCategories.push(newCategory);
    return id;
  }

  static async update(id: string, updates: Partial<MainCategory>): Promise<void> {
    const index = mockMainCategories.findIndex(c => c.id === id);
    if (index !== -1) {
      mockMainCategories[index] = { ...mockMainCategories[index], ...updates };
    }
  }

  static async delete(id: string): Promise<void> {
    mockMainCategories = mockMainCategories.filter(c => c.id !== id);
  }
}

export class MockViolationService {
  static async getAll(): Promise<Violation[]> {
    return [...mockViolations];
  }

  static async getByMainCategoryId(mainCategoryId: string): Promise<Violation[]> {
    return mockViolations.filter(v => v.mainCategoryId === mainCategoryId);
  }

  static async getById(id: string): Promise<Violation | null> {
    return mockViolations.find(v => v.id === id) || null;
  }

  static async create(data: Omit<Violation, 'id'>): Promise<string> {
    const id = generateId();
    const newViolation: Violation = { ...data, id };
    mockViolations.push(newViolation);
    return id;
  }

  static async update(id: string, updates: Partial<Violation>): Promise<void> {
    const index = mockViolations.findIndex(v => v.id === id);
    if (index !== -1) {
      mockViolations[index] = { ...mockViolations[index], ...updates };
    }
  }

  static async delete(id: string): Promise<void> {
    mockViolations = mockViolations.filter(v => v.id !== id);
  }
}

export class MockProvinceService {
  static async getAll(): Promise<Province[]> {
    return [...mockProvinces];
  }

  static async getById(id: string): Promise<Province | null> {
    return mockProvinces.find(p => p.id === id) || null;
  }
}

export class MockSourceService {
  static async getAll(): Promise<Source[]> {
    return [...mockSources];
  }

  static async getById(id: string): Promise<Source | null> {
    return mockSources.find(s => s.id === s) || null;
  }
}

export class MockDynamicFieldService {
  static async getAll(): Promise<DynamicField[]> {
    return [...mockDynamicFields];
  }

  static async getByViolationId(violationId: string): Promise<DynamicField[]> {
    return mockDynamicFields.filter(f => f.violationId === violationId).sort((a, b) => a.order - b.order);
  }

  static async getById(id: string): Promise<DynamicField | null> {
    return mockDynamicFields.find(f => f.id === id) || null;
  }

  static async create(data: Omit<DynamicField, 'id'>): Promise<string> {
    const id = generateId();
    const newField: DynamicField = { ...data, id };
    mockDynamicFields.push(newField);
    return id;
  }

  static async update(id: string, updates: Partial<DynamicField>): Promise<void> {
    const index = mockDynamicFields.findIndex(f => f.id === id);
    if (index !== -1) {
      mockDynamicFields[index] = { ...mockDynamicFields[index], ...updates };
    }
  }

  static async delete(id: string): Promise<void> {
    mockDynamicFields = mockDynamicFields.filter(f => f.id !== id);
  }
}

export class MockFieldValueService {
  static async getAll(): Promise<FieldValue[]> {
    return [...mockFieldValues];
  }

  static async getByNewsId(newsId: string): Promise<FieldValue[]> {
    return mockFieldValues.filter(v => v.newsId === newsId);
  }

  static async create(data: Omit<FieldValue, 'id'>): Promise<string> {
    const id = generateId();
    const newValue: FieldValue = { ...data, id };
    mockFieldValues.push(newValue);
    return id;
  }

  static async update(id: string, updates: Partial<FieldValue>): Promise<void> {
    const index = mockFieldValues.findIndex(v => v.id === id);
    if (index !== -1) {
      mockFieldValues[index] = { ...mockFieldValues[index], ...updates };
    }
  }

  static async delete(id: string): Promise<void> {
    mockFieldValues = mockFieldValues.filter(v => v.id !== id);
  }

  static async deleteByNewsId(newsId: string): Promise<void> {
    mockFieldValues = mockFieldValues.filter(v => v.newsId !== newsId);
  }
}

// Initialize data when module loads
console.log('Initializing mock data...');
initializeMockData();
console.log('Mock data initialized. Main categories count:', mockMainCategories.length);
