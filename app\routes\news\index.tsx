import { useState, useEffect } from "react";
import { Link, useNavigate } from "react-router";
import { Layout } from "~/components/Layout";
import { NewsCard } from "~/components/NewsCard";
import {
  Search,
  Filter,
  Plus,
  Calendar,
  MapPin,
  Tags,
  SortAsc,
  SortDesc,
  Grid,
  List,
  Edit,
  Trash2,
  Eye,
  User,
  Tag,
  AlertTriangle,
  FileText,
  Clock
} from "lucide-react";
import type { NewsArticle, Classification, Source } from "~/types";
import {
  MockNewsService as NewsService,
  MockClassificationService as ClassificationService,
  MockSourceService as SourceService
} from "~/lib/mockData";

// Mock data
const mockNews = [
  {
    id: "1",
    title: "تقرير جديد حول انتهاكات حقوق الإنسان في بغداد",
    content: "كشف تقرير حديث عن ارتفاع في عدد انتهاكات حقوق الإنسان في العاصمة بغداد خلال الشهر الماضي، حيث تم توثيق عدة حالات تتطلب تدخلاً عاجلاً من الجهات المختصة...",
    publishedDate: new Date(),
    province: { id: "1", name: "بغداد", createdAt: new Date() },
    source: { id: "1", name: "وكالة الأنباء العراقية", type: "website" as const, createdAt: new Date() },
    category: { id: "1", name: "انتهاكات حقوق الإنسان", dynamicFields: [], createdAt: new Date(), updatedAt: new Date() },
    viewCount: 156,
    tags: ["حقوق الإنسان", "بغداد", "انتهاكات"],
    dynamicData: { "عدد الضحايا": "12", "نوع الانتهاك": "اعتقال تعسفي" },
    createdAt: new Date(),
    updatedAt: new Date(),
    createdBy: "user1",
    status: "published" as const,
    isPublic: true
  },
  {
    id: "2",
    title: "حملة توعية حول حقوق المرأة في البصرة",
    content: "انطلقت اليوم حملة توعية واسعة حول حقوق المرأة في محافظة البصرة بمشاركة منظمات المجتمع المدني والجهات الحكومية المختصة...",
    publishedDate: new Date(Date.now() - 86400000),
    province: { id: "2", name: "البصرة", createdAt: new Date() },
    source: { id: "2", name: "إذاعة البصرة", type: "radio" as const, createdAt: new Date() },
    category: { id: "2", name: "حقوق المرأة", dynamicFields: [], createdAt: new Date(), updatedAt: new Date() },
    viewCount: 89,
    tags: ["حقوق المرأة", "البصرة", "توعية"],
    dynamicData: { "عدد المشاركين": "200", "مدة الحملة": "أسبوع واحد" },
    createdAt: new Date(),
    updatedAt: new Date(),
    createdBy: "user2",
    status: "published" as const,
    isPublic: true
  },
  {
    id: "3",
    title: "ورشة عمل حول حقوق الطفل في أربيل",
    content: "نظمت منظمة حقوق الإنسان ورشة عمل متخصصة حول حقوق الطفل في محافظة أربيل، بحضور خبراء محليين ودوليين...",
    publishedDate: new Date(Date.now() - 172800000),
    province: { id: "3", name: "أربيل", createdAt: new Date() },
    source: { id: "3", name: "تلفزيون كردستان", type: "tv" as const, createdAt: new Date() },
    category: { id: "3", name: "حقوق الطفل", dynamicFields: [], createdAt: new Date(), updatedAt: new Date() },
    viewCount: 67,
    tags: ["حقوق الطفل", "أربيل", "ورشة عمل"],
    dynamicData: { "عدد الحضور": "50", "عدد الخبراء": "8" },
    createdAt: new Date(),
    updatedAt: new Date(),
    createdBy: "user3",
    status: "published" as const,
    isPublic: true
  }
];

const mockCategories = [
  { id: "1", name: "انتهاكات حقوق الإنسان" },
  { id: "2", name: "حقوق المرأة" },
  { id: "3", name: "حقوق الطفل" },
  { id: "4", name: "الحريات العامة" },
];

const mockProvinces = [
  { id: "1", name: "بغداد" },
  { id: "2", name: "البصرة" },
  { id: "3", name: "أربيل" },
  { id: "4", name: "النجف" },
];

export default function NewsIndex() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("");
  const [selectedProvince, setSelectedProvince] = useState("");
  const [sortBy, setSortBy] = useState("date");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [showFilters, setShowFilters] = useState(false);

  const filteredNews = mockNews.filter(article => {
    const matchesSearch = article.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         article.content.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = !selectedCategory || article.category?.id === selectedCategory;
    const matchesProvince = !selectedProvince || article.province?.id === selectedProvince;
    
    return matchesSearch && matchesCategory && matchesProvince;
  });

  const sortedNews = [...filteredNews].sort((a, b) => {
    let comparison = 0;
    
    switch (sortBy) {
      case "date":
        comparison = a.publishedDate.getTime() - b.publishedDate.getTime();
        break;
      case "title":
        comparison = a.title.localeCompare(b.title, 'ar');
        break;
      case "views":
        comparison = (a.viewCount || 0) - (b.viewCount || 0);
        break;
      default:
        comparison = 0;
    }
    
    return sortOrder === "asc" ? comparison : -comparison;
  });

  return (
    <Layout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">عرض الأخبار</h1>
            <p className="text-gray-600">إدارة وعرض جميع الأخبار في النظام</p>
          </div>
          <Link
            to="/news/add"
            className="mt-4 sm:mt-0 inline-flex items-center px-4 py-2 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Plus className="h-5 w-5 ml-2" />
            إضافة خبر جديد
          </Link>
        </div>

        {/* Search and Filters */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <div className="flex flex-col lg:flex-row gap-4">
            {/* Search */}
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                <input
                  type="text"
                  placeholder="البحث في الأخبار..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>

            {/* Filter Toggle */}
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <Filter className="h-5 w-5 ml-2" />
              الفلاتر
            </button>

            {/* View Mode Toggle */}
            <div className="flex border border-gray-300 rounded-lg overflow-hidden">
              <button
                onClick={() => setViewMode("grid")}
                className={`px-3 py-2 ${viewMode === "grid" ? "bg-blue-600 text-white" : "bg-white text-gray-700 hover:bg-gray-50"} transition-colors`}
              >
                <Grid className="h-5 w-5" />
              </button>
              <button
                onClick={() => setViewMode("list")}
                className={`px-3 py-2 ${viewMode === "list" ? "bg-blue-600 text-white" : "bg-white text-gray-700 hover:bg-gray-50"} transition-colors`}
              >
                <List className="h-5 w-5" />
              </button>
            </div>
          </div>

          {/* Filters */}
          {showFilters && (
            <div className="mt-6 pt-6 border-t border-gray-200">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <Tags className="inline h-4 w-4 ml-1" />
                    التصنيف
                  </label>
                  <select
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">جميع التصنيفات</option>
                    {mockCategories.map(category => (
                      <option key={category.id} value={category.id}>
                        {category.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <MapPin className="inline h-4 w-4 ml-1" />
                    المحافظة
                  </label>
                  <select
                    value={selectedProvince}
                    onChange={(e) => setSelectedProvince(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">جميع المحافظات</option>
                    {mockProvinces.map(province => (
                      <option key={province.id} value={province.id}>
                        {province.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <Calendar className="inline h-4 w-4 ml-1" />
                    ترتيب حسب
                  </label>
                  <div className="flex gap-2">
                    <select
                      value={sortBy}
                      onChange={(e) => setSortBy(e.target.value)}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="date">التاريخ</option>
                      <option value="title">العنوان</option>
                      <option value="views">المشاهدات</option>
                    </select>
                    <button
                      onClick={() => setSortOrder(sortOrder === "asc" ? "desc" : "asc")}
                      className="px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      {sortOrder === "asc" ? <SortAsc className="h-5 w-5" /> : <SortDesc className="h-5 w-5" />}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Results */}
        <div className="mb-6">
          <p className="text-gray-600">
            عرض {sortedNews.length} من أصل {mockNews.length} خبر
          </p>
        </div>

        {/* News Grid/List */}
        <div className={viewMode === "grid" ? "grid grid-cols-1 lg:grid-cols-2 gap-6" : "space-y-6"}>
          {sortedNews.map((article) => (
            <NewsCard 
              key={article.id} 
              article={article} 
              showActions={true}
              onEdit={(id) => console.log("Edit:", id)}
              onDelete={(id) => console.log("Delete:", id)}
            />
          ))}
        </div>

        {sortedNews.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500 text-lg">لا توجد أخبار تطابق معايير البحث</p>
          </div>
        )}
      </div>
    </Layout>
  );
}
