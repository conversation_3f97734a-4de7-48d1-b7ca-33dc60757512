import { useState, useEffect } from 'react';
import { Calendar, FileText, Hash, Type, ToggleLeft, ToggleRight } from 'lucide-react';
import type { ClassificationField, ClassificationFieldOption } from '~/types';
import { MockClassificationFieldService as ClassificationFieldService, MockFieldOptionService as FieldOptionService } from '~/lib/mockData';

interface DynamicFieldsProps {
  classificationId: string;
  values: Record<string, string>;
  onChange: (fieldId: string, value: string) => void;
  errors?: Record<string, string>;
}

export function DynamicFields({ classificationId, values, onChange, errors }: DynamicFieldsProps) {
  const [fields, setFields] = useState<ClassificationField[]>([]);
  const [fieldOptions, setFieldOptions] = useState<Record<string, ClassificationFieldOption[]>>({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadFields();
  }, [classificationId]);

  const loadFields = async () => {
    if (!classificationId) {
      setFields([]);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      const fieldsData = await ClassificationFieldService.getByClassificationId(classificationId);
      setFields(fieldsData);

      // Load options for select fields
      const optionsData: Record<string, ClassificationFieldOption[]> = {};
      for (const field of fieldsData) {
        if (field.type === 'select') {
          const options = await FieldOptionService.getByFieldId(field.id);
          optionsData[field.id] = options;
        }
      }
      setFieldOptions(optionsData);
    } catch (error) {
      console.error('Error loading fields:', error);
    } finally {
      setLoading(false);
    }
  };

  const renderField = (field: ClassificationField) => {
    const value = values[field.id] || '';
    const error = errors?.[field.id];
    const fieldId = `field-${field.id}`;

    const baseClasses = `w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
      error ? 'border-red-300' : 'border-gray-300'
    }`;

    const handleChange = (newValue: string) => {
      onChange(field.id, newValue);
    };

    const getFieldIcon = () => {
      switch (field.type) {
        case 'text':
        case 'textarea':
          return <Type className="h-4 w-4 text-gray-400" />;
        case 'number':
          return <Hash className="h-4 w-4 text-gray-400" />;
        case 'date':
          return <Calendar className="h-4 w-4 text-gray-400" />;
        case 'select':
          return <FileText className="h-4 w-4 text-gray-400" />;
        case 'boolean':
          return value === 'true' ? 
            <ToggleRight className="h-4 w-4 text-green-500" /> : 
            <ToggleLeft className="h-4 w-4 text-gray-400" />;
        default:
          return <Type className="h-4 w-4 text-gray-400" />;
      }
    };

    return (
      <div key={field.id} className="space-y-2">
        <label htmlFor={fieldId} className="flex items-center text-sm font-medium text-gray-700">
          {getFieldIcon()}
          <span className="mr-2">
            {field.label}
            {field.required && <span className="text-red-500 mr-1">*</span>}
          </span>
        </label>

        {field.type === 'text' && (
          <input
            type="text"
            id={fieldId}
            value={value}
            onChange={(e) => handleChange(e.target.value)}
            className={baseClasses}
            placeholder={`أدخل ${field.label}`}
            required={field.required}
          />
        )}

        {field.type === 'number' && (
          <input
            type="number"
            id={fieldId}
            value={value}
            onChange={(e) => handleChange(e.target.value)}
            className={baseClasses}
            placeholder={`أدخل ${field.label}`}
            required={field.required}
            min={field.validation?.min}
            max={field.validation?.max}
          />
        )}

        {field.type === 'textarea' && (
          <textarea
            id={fieldId}
            value={value}
            onChange={(e) => handleChange(e.target.value)}
            className={baseClasses}
            placeholder={`أدخل ${field.label}`}
            required={field.required}
            rows={4}
          />
        )}

        {field.type === 'date' && (
          <input
            type="date"
            id={fieldId}
            value={value}
            onChange={(e) => handleChange(e.target.value)}
            className={baseClasses}
            required={field.required}
          />
        )}

        {field.type === 'select' && (
          <select
            id={fieldId}
            value={value}
            onChange={(e) => handleChange(e.target.value)}
            className={baseClasses}
            required={field.required}
          >
            <option value="">اختر {field.label}</option>
            {fieldOptions[field.id]?.map((option) => (
              <option key={option.id} value={option.value}>
                {option.value}
              </option>
            ))}
          </select>
        )}

        {field.type === 'boolean' && (
          <div className="flex items-center space-x-3 space-x-reverse">
            <button
              type="button"
              onClick={() => handleChange(value === 'true' ? 'false' : 'true')}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                value === 'true' ? 'bg-blue-600' : 'bg-gray-200'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  value === 'true' ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
            <span className="text-sm text-gray-700">
              {value === 'true' ? 'نعم' : 'لا'}
            </span>
          </div>
        )}

        {error && (
          <p className="text-sm text-red-600">{error}</p>
        )}
      </div>
    );
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
          <div className="h-10 bg-gray-200 rounded"></div>
        </div>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/3 mb-2"></div>
          <div className="h-10 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  if (!classificationId) {
    return (
      <div className="text-center py-8 text-gray-500">
        <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
        <p>يرجى اختيار التصنيف أولاً لعرض الحقول المطلوبة</p>
      </div>
    );
  }

  if (fields.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
        <p>لا توجد حقول إضافية لهذا التصنيف</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="border-t border-gray-200 pt-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          الحقول الإضافية للتصنيف
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {fields.map(renderField)}
        </div>
      </div>
    </div>
  );
}
