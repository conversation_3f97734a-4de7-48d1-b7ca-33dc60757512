import { useState, useEffect } from 'react';
import { Link } from 'react-router';
import { Plus, Search, Edit, Trash2, Eye, Filter } from 'lucide-react';
import { Violation, MainCategory } from '~/types';
import { MockViolationService as ViolationService, MockMainCategoryService as MainCategoryService } from '~/lib/mockData';

export default function ViolationsIndex() {
  const [violations, setViolations] = useState<Violation[]>([]);
  const [mainCategories, setMainCategories] = useState<MainCategory[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [violationsData, categoriesData] = await Promise.all([
        ViolationService.getAll(),
        MainCategoryService.getAll()
      ]);
      setViolations(violationsData);
      setMainCategories(categoriesData);
    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    if (confirm('هل أنت متأكد من حذف هذا الانتهاك؟')) {
      try {
        await ViolationService.delete(id);
        await loadData();
      } catch (error) {
        console.error('Error deleting violation:', error);
      }
    }
  };

  const filteredViolations = violations.filter(violation => {
    const matchesSearch = violation.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         violation.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = !selectedCategory || violation.mainCategoryId === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const getCategoryName = (categoryId: string) => {
    const category = mainCategories.find(c => c.id === categoryId);
    return category?.name || 'غير محدد';
  };

  const getActiveViolationsCount = () => violations.filter(v => v.isActive).length;
  const getInactiveViolationsCount = () => violations.filter(v => !v.isActive).length;

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جار تحميل الانتهاكات...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">إدارة الانتهاكات</h1>
              <p className="mt-2 text-gray-600">إدارة الانتهاكات الفرعية المرتبطة بالتصنيفات الرئيسية</p>
            </div>
            <Link
              to="/violations/add"
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <Plus className="h-4 w-4 ml-2" />
              إضافة انتهاك جديد
            </Link>
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <Eye className="h-5 w-5 text-blue-600" />
                  </div>
                </div>
                <div className="mr-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">إجمالي الانتهاكات</dt>
                    <dd className="text-lg font-medium text-gray-900">{violations.length}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  </div>
                </div>
                <div className="mr-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">الانتهاكات النشطة</dt>
                    <dd className="text-lg font-medium text-gray-900">{getActiveViolationsCount()}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                    <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  </div>
                </div>
                <div className="mr-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">الانتهاكات غير النشطة</dt>
                    <dd className="text-lg font-medium text-gray-900">{getInactiveViolationsCount()}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                    <Filter className="h-5 w-5 text-purple-600" />
                  </div>
                </div>
                <div className="mr-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">التصنيفات الرئيسية</dt>
                    <dd className="text-lg font-medium text-gray-900">{mainCategories.length}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Search and Filter */}
        <div className="bg-white shadow rounded-lg mb-6">
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="relative">
                <Search className="absolute right-3 top-3 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="البحث في الانتهاكات..."
                  className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <div>
                <select
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                >
                  <option value="">جميع التصنيفات الرئيسية</option>
                  {mainCategories.map(category => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Violations Table */}
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          <div className="px-4 py-5 sm:px-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900">
              قائمة الانتهاكات ({filteredViolations.length})
            </h3>
          </div>
          <ul className="divide-y divide-gray-200">
            {filteredViolations.map((violation) => (
              <li key={violation.id}>
                <div className="px-4 py-4 sm:px-6">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-blue-600 truncate">
                            {violation.name}
                          </p>
                          <p className="text-sm text-gray-500 mt-1">
                            {violation.description}
                          </p>
                          <div className="mt-2 flex items-center text-sm text-gray-500">
                            <span className="ml-4">التصنيف الرئيسي: {getCategoryName(violation.mainCategoryId)}</span>
                            <span className="ml-4">الحق المرتبط: {violation.relatedRight}</span>
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ml-4 ${
                              violation.isActive 
                                ? 'bg-green-100 text-green-800' 
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {violation.isActive ? 'نشط' : 'غير نشط'}
                            </span>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <Link
                            to={`/violations/${violation.id}`}
                            className="text-blue-600 hover:text-blue-900"
                          >
                            <Eye className="h-4 w-4" />
                          </Link>
                          <Link
                            to={`/violations/${violation.id}/edit`}
                            className="text-green-600 hover:text-green-900"
                          >
                            <Edit className="h-4 w-4" />
                          </Link>
                          <button
                            onClick={() => handleDelete(violation.id)}
                            className="text-red-600 hover:text-red-900"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </li>
            ))}
          </ul>
          
          {filteredViolations.length === 0 && (
            <div className="text-center py-12">
              <p className="text-gray-500">لا توجد انتهاكات مطابقة للبحث</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
