<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="robots" content="noindex, nofollow">
    <title>الإحصائيات</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/bootstrap.rtl.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <style>
        @page {
            size: A4;
            margin: 1cm;
        }
        body {
            font-family: 'Tajawal', sans-serif;
            background-color: white;
            color: #333;
            padding: 0;
            margin: 0;
        }
        .print-container {
            max-width: 1140px;
            margin: 0 auto;
            padding: 20px;
        }
        .print-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 2px solid #0d6efd;
            text-align: center;
        }
        .print-header-logo {
            max-height: 80px;
        }
        .print-header-content {
            text-align: center;
            flex-grow: 1;
            margin: 0 20px;
        }
        .print-title {
            font-size: 28px;
            font-weight: bold;
            margin: 5px 0;
            color: #0d6efd;
        }
        .print-subtitle {
            font-size: 20px;
            color: #333;
            margin: 5px 0;
        }
        .print-date {
            font-size: 16px;
            color: #6c757d;
            margin: 5px 0;
        }
        .report-section {
            margin-bottom: 30px;
            page-break-inside: avoid;
        }
        .category-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: linear-gradient(to left, #0d6efd, #0a58ca);
            color: white;
            padding: 12px 20px;
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: 0 3px 6px rgba(0,0,0,0.1);
        }
        .category-title {
            font-size: 20px;
            font-weight: bold;
            margin: 0;
        }
        .category-count {
            background-color: white;
            color: #0d6efd;
            padding: 5px 15px;
            border-radius: 30px;
            font-size: 16px;
            font-weight: bold;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            margin-bottom: 30px;
        }
        .stats-card {
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            background-color: #f8f9fa;
            box-shadow: 0 3px 10px rgba(0,0,0,0.08);
            text-align: center;
        }
        .stats-value {
            font-size: 36px;
            font-weight: bold;
            color: #0d6efd;
            text-align: center;
            margin-bottom: 10px;
        }
        .stats-label {
            font-size: 16px;
            color: #495057;
            text-align: center;
            border-top: 1px dashed #dee2e6;
            padding-top: 10px;
        }
        .footer {
            text-align: center;
            margin-top: 50px;
            padding-top: 20px;
            border-top: 2px solid #0d6efd;
        }
        .footer-content {
            text-align: center;
        }
        .footer-title {
            font-size: 18px;
            font-weight: bold;
            color: #0d6efd;
            margin-bottom: 5px;
        }
        .footer-text {
            font-size: 14px;
            color: #6c757d;
        }

        /* تحسينات للتوزيع المتوازن */
        .report-section {
            margin-bottom: 20px;
            page-break-inside: avoid;
        }

        .stats-balanced-card {
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            height: 100%;
        }

        .stats-balanced-card .card-header {
            border-radius: 8px 8px 0 0;
            padding: 0.75rem;
        }

        .stats-main-number {
            font-size: 2.5rem;
            font-weight: 700;
            line-height: 1;
        }

        .stats-item-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            transition: background-color 0.2s ease;
        }

        .stats-item-box:hover {
            background: #e9ecef;
        }

        .stats-scrollable-area {
            max-height: 280px;
            overflow-y: auto;
            padding-right: 5px;
        }

        .stats-scrollable-area::-webkit-scrollbar {
            width: 6px;
        }

        .stats-scrollable-area::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .stats-scrollable-area::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 3px;
        }

        .stats-scrollable-area::-webkit-scrollbar-thumb:hover {
            background: #555;
        }

        @media print {
            .no-print {
                display: none;
            }
            .page-break {
                page-break-after: always;
            }
            body {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
            .stats-main-card {
                box-shadow: none;
                border: 2px solid #0d6efd;
            }
            .stats-sub-card {
                box-shadow: none;
                border: 1px solid #dee2e6;
            }
        }
    </style>
</head>
<body>
    <div class="print-container">
        <div class="no-print mb-3 text-center">
            <button onclick="window.print()" class="btn btn-primary btn-lg">
                <i class="fas fa-print"></i> طباعة التقرير
            </button>
            <a href="{{ url_for('statistics') }}" class="btn btn-secondary btn-lg">
                <i class="fas fa-arrow-right"></i> العودة للإحصائيات
            </a>
        </div>

        <div class="print-header">
            <img src="{{ url_for('static', filename='img/ihchr.png') }}" alt="شعار المفوضية" class="print-header-logo">
            <div class="print-header-content">
                <div class="print-title">الإحصائيات</div>
                <div class="print-subtitle">نظام الرصد الإعلامي</div>
                <div class="print-date">{{ today_date }}
                    {% if date_filter_text %}
                    <br><small>{{ date_filter_text }}</small>
                    {% endif %}
                </div>
            </div>
            <img src="{{ url_for('static', filename='img/logo2.png') }}" alt="شعار النظام" class="print-header-logo">
        </div>

        <!-- الإحصائيات في تخطيط موحد ومتوازن -->
        <div class="report-section">
            <div class="category-header">
                <h2 class="category-title">
                    <i class="fas fa-chart-pie me-2"></i>
                    الإحصائيات العامة والتفصيلية
                    {% if date_filter_text %}
                    <small class="text-muted fs-6 fw-normal me-2">({{ date_filter_text }})</small>
                    {% endif %}
                </h2>
            </div>

            <!-- الصف الأول: إجمالي الأخبار + التصنيفات -->
            <div class="row g-3 mb-3">
                <!-- إجمالي الأخبار -->
                <div class="col-4">
                    <div class="card stats-balanced-card border-primary">
                        <div class="card-header bg-primary text-white text-center">
                            <h6 class="mb-0">
                                <i class="fas fa-newspaper me-2"></i>
                                إجمالي الأخبار المرصودة
                            </h6>
                        </div>
                        <div class="card-body text-center d-flex align-items-center justify-content-center">
                            <div>
                                <h1 class="text-primary fw-bold mb-2 stats-main-number">{{ "{:,}".format(total_news) }}</h1>
                                <p class="text-muted mb-0">خبر مرصود</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- التصنيفات -->
                {% if category_stats %}
                <div class="col-8">
                    <div class="card stats-balanced-card">
                        <div class="card-header bg-success text-white text-center">
                            <h6 class="mb-0">
                                <i class="fas fa-tags me-2"></i>
                                الأخبار حسب التصنيف
                            </h6>
                        </div>
                        <div class="card-body p-2">
                            <div class="row g-2">
                                {% for stat in category_stats %}
                                <div class="col-6 col-lg-4">
                                    <div class="stats-item-box d-flex align-items-center justify-content-between">
                                        <div class="d-flex align-items-center">
                                            <span class="badge bg-success me-2">{{ loop.index }}</span>
                                            <span class="fw-bold">{{ stat.name }}</span>
                                        </div>
                                        <div class="text-end">
                                            <div class="text-success fw-bold">{{ stat.news_count }}</div>
                                            <small class="text-muted">{{ (stat.news_count / total_news * 100)|round(0)|int }}%</small>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>

            <!-- الصف الثاني: المحافظات + المصادر -->
            <div class="row g-3">
                <!-- المحافظات -->
                {% if governorate_stats %}
                <div class="col-6">
                    <div class="card stats-balanced-card">
                        <div class="card-header bg-warning text-white text-center">
                            <h6 class="mb-0">
                                <i class="fas fa-map-marker-alt me-2"></i>
                                الأخبار حسب المحافظة
                            </h6>
                        </div>
                        <div class="card-body p-2">
                            <div class="row g-2">
                                {% for stat in governorate_stats %}
                                <div class="col-12 col-lg-6">
                                    <div class="stats-item-box d-flex align-items-center justify-content-between">
                                        <div class="d-flex align-items-center">
                                            <span class="badge bg-warning me-2">{{ loop.index }}</span>
                                            <span class="fw-bold">{{ stat.name }}</span>
                                        </div>
                                        <div class="text-end">
                                            <div class="text-warning fw-bold">{{ stat.news_count }}</div>
                                            <small class="text-muted">{{ (stat.news_count / total_news * 100)|round(0)|int }}%</small>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}

                <!-- المصادر -->
                {% if source_stats %}
                <div class="col-6">
                    <div class="card stats-balanced-card">
                        <div class="card-header bg-info text-white text-center">
                            <h6 class="mb-0">
                                <i class="fas fa-rss me-2"></i>
                                الأخبار حسب المصدر
                            </h6>
                        </div>
                        <div class="card-body p-2">
                            <div class="stats-scrollable-area">
                                <div class="row g-2">
                                    {% for stat in source_stats %}
                                    <div class="col-12">
                                        <div class="stats-item-box d-flex align-items-center justify-content-between">
                                            <div class="d-flex align-items-center">
                                                <span class="badge bg-info me-2">{{ loop.index }}</span>
                                                <span class="fw-bold">{{ stat.source }}</span>
                                            </div>
                                            <div class="text-end">
                                                <div class="text-info fw-bold">{{ stat.news_count }}</div>
                                                <small class="text-muted">{{ (stat.news_count / total_news * 100)|round(0)|int }}%</small>
                                            </div>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- تفاصيل التصنيفات والحقول الإضافية -->
        {% if field_stats %}
        <div class="report-section">
            <div class="category-header">
                <h2 class="category-title">تفاصيل التصنيفات والحقول الإضافية</h2>
            </div>
        </div>
        {% endif %}

        {% for category in field_stats %}
        {% if category.fields|length > 0 %}
        <div class="report-section">
            <div class="category-header">
                <h2 class="category-title">{{ category.category_name }}</h2>
                <span class="category-count">عدد الأخبار: {{ category.news_count }}</span>
            </div>

            <!-- الحقول الرقمية -->
            {% set numeric_fields = category.fields|selectattr('field_type', 'equalto', 'number')|selectattr('count', 'greaterthan', 0)|list %}
            {% if numeric_fields|length > 0 %}
            <h3 style="color: #0d6efd; margin-bottom: 20px; border-bottom: 2px solid #0d6efd; padding-bottom: 10px;">الإحصائيات الرقمية</h3>
            <div class="stats-grid">
                {% for field in numeric_fields %}
                <div class="stats-card">
                    <div class="stats-value">{{ field.total }}</div>
                    <div class="stats-label">{{ field.name }}</div>
                    <div style="font-size: 12px; color: #6c757d; margin-top: 5px;">
                        المتوسط: {{ field.average }} | الاستجابة: {{ field.response_rate }}%
                    </div>
                </div>
                {% endfor %}
            </div>
            {% endif %}

            <!-- الحقول الأخرى -->
            {% set other_fields = category.fields|rejectattr('field_type', 'equalto', 'number')|selectattr('total_responses', 'greaterthan', 0)|list %}
            {% if other_fields|length > 0 %}
            <h3 style="color: #0d6efd; margin-bottom: 20px; border-bottom: 2px solid #0d6efd; padding-bottom: 10px;">تفاصيل الحقول الأخرى</h3>
            {% for field in other_fields %}
            <div style="margin-bottom: 30px; border: 1px solid #dee2e6; border-radius: 8px; padding: 20px; background-color: #f8f9fa;">
                <h4 style="color: #0d6efd; margin-bottom: 15px;">{{ field.name }}</h4>
                <div style="margin-bottom: 15px; font-size: 14px; color: #6c757d;">
                    إجمالي الإجابات: {{ field.total_responses }} | معدل الاستجابة: {{ field.response_rate }}%
                </div>
                <table style="width: 100%; border-collapse: collapse; font-size: 14px;">
                    <thead>
                        <tr style="background-color: #0d6efd; color: white;">
                            <th style="padding: 10px; border: 1px solid #dee2e6; text-align: right;">القيمة</th>
                            <th style="padding: 10px; border: 1px solid #dee2e6; text-align: center;">العدد</th>
                            <th style="padding: 10px; border: 1px solid #dee2e6; text-align: center;">النسبة</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in field.value_items[:10] %}
                        <tr style="{% if loop.index % 2 == 0 %}background-color: #f8f9fa;{% endif %}">
                            <td style="padding: 8px; border: 1px solid #dee2e6;">{{ item.value }}</td>
                            <td style="padding: 8px; border: 1px solid #dee2e6; text-align: center;">{{ item.value_count }}</td>
                            <td style="padding: 8px; border: 1px solid #dee2e6; text-align: center;">{{ item.percentage }}%</td>
                        </tr>
                        {% endfor %}
                        {% if field.value_items|length > 10 %}
                        <tr>
                            <td colspan="3" style="padding: 8px; border: 1px solid #dee2e6; text-align: center; font-style: italic; color: #6c757d;">
                                ... وعدد {{ field.value_items|length - 10 }} قيمة أخرى
                            </td>
                        </tr>
                        {% endif %}
                    </tbody>
                </table>
            </div>
            {% endfor %}
            {% endif %}
        </div>
        {% if not loop.last %}<div class="page-break"></div>{% endif %}
        {% endif %}
        {% endfor %}

        <div class="footer">
            <div class="footer-content">
                <div class="footer-title">نظام الرصد الإعلامي</div>
                <div class="footer-text">{{ today_date }}</div>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/bootstrap.bundle.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/fontawesome.min.js') }}"></script>
    <script>
        // تعيين عنوان الصفحة
        document.title = "الإحصائيات";

        // تنفيذ الطباعة تلقائياً عند الضغط على Ctrl+P
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                window.print();
            }
        });

        // إزالة الزوائد من URL الصفحة في شريط العنوان
        if (window.history && window.history.replaceState) {
            window.history.replaceState({}, "الإحصائيات", "/");
        }

        // تغيير عنوان الصفحة بشكل دوري للتأكد من عدم ظهور الزوائد
        setInterval(function() {
            if (document.title !== "الإحصائيات") {
                document.title = "الإحصائيات";
            }
        }, 100);
    </script>
</body>
</html>
