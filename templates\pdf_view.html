<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير الأخبار</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        @page {
            size: A4;
            margin: 0.5cm;
        }

        @media print {
            body {
                font-size: 12pt;
                margin: 0;
                padding: 0;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
            .no-print {
                display: none !important;
            }
            .page-break {
                page-break-after: always;
            }
            .header {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
            .date-bar {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
            .news-title {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
            .news-meta {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
        }

        body {
            font-family: 'Tajawal', Arial, sans-serif;
            margin: 0;
            padding: 0;
            color: #333;
            direction: rtl;
            line-height: 1.6;
            background-color: #e6f2ff;
        }

        html {
            background-color: #e6f2ff;
            background-image: linear-gradient(135deg, #e6f2ff 0%, #f0f8ff 50%, #e6f7ff 100%);
            min-height: 100%;
            position: relative;
        }

        html::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: repeating-linear-gradient(45deg, rgba(0, 86, 179, 0.02) 0, rgba(0, 86, 179, 0.02) 1px, transparent 1px, transparent 50px);
            z-index: -1;
        }

        .header {
            background: linear-gradient(135deg, #0056b3 0%, #003a75 100%);
            color: white;
            text-align: center;
            padding: 15px 0;
            margin-bottom: 15px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
            width: 100%;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSJub25lIi8+PGNpcmNsZSBjeD0iMTAiIGN5PSIxMCIgcj0iMiIgZmlsbD0icmdiYSgyNTUsMjU1LDI1NSwwLjEpIi8+PC9zdmc+');
            opacity: 0.3;
        }

        .logo {
            width: 120px;
            height: auto;
            margin: 0 auto 10px;
            display: block;
            filter: drop-shadow(0 2px 5px rgba(0, 0, 0, 0.2));
        }

        .title {
            font-size: 28px;
            font-weight: 700;
            margin: 10px 0 5px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .subtitle {
            font-size: 16px;
            margin: 3px 0;
            font-weight: 500;
        }

        .department {
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 4px 15px;
            display: inline-block;
            margin: 5px 3px;
            font-weight: 500;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            font-size: 14px;
        }

        .date-bar {
            background: linear-gradient(90deg, #0056b3 0%, #003a75 100%);
            color: white;
            padding: 8px 15px;
            text-align: left;
            margin: 15px 0;
            border-top: 1px solid rgba(255, 255, 255, 0.3);
            border-bottom: 1px solid rgba(255, 255, 255, 0.3);
            font-weight: 500;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            position: relative;
            font-size: 14px;
        }

        /* تم إزالة أيقونة التقويم من شريط التاريخ */

        .news-item {
            border: none;
            margin-bottom: 25px;
            page-break-inside: avoid;
            background-color: white;
            border-radius: 6px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border-top: 3px solid #0056b3;
        }

        .news-title {
            background: linear-gradient(90deg, #f8f8f8 0%, #f2f2f2 100%);
            border-right: 5px solid #c00;
            padding: 10px 15px;
            font-weight: 700;
            font-size: 18px;
            color: #c00;
            position: relative;
        }

        .news-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, rgba(0,0,0,0.1) 0%, rgba(0,0,0,0.02) 100%);
        }

        .news-content {
            padding: 15px 20px;
            line-height: 1.6;
            color: #444;
            font-size: 14px;
            text-align: justify;
            background-color: white;
        }

        .news-meta {
            background-color: #f8f8f8;
            padding: 8px 15px;
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #666;
            border-top: 1px solid #eee;
        }

        .meta-item {
            display: inline-block;
            margin-left: 15px;
        }

        .meta-item:before {
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            margin-left: 5px;
            color: #0056b3;
        }

        .meta-date:before {
            content: '\f073';
        }

        .meta-source:before {
            content: '\f1ea';
        }

        .meta-location:before {
            content: '\f3c5';
        }

        .footer {
            text-align: center;
            margin-top: 30px;
            padding: 15px 0;
            font-size: 11px;
            color: #777;
            border-top: 1px solid #eee;
            background-color: white;
        }

        .container {
            max-width: 1140px;
            margin: 0 auto;
            padding: 0 10px;
        }

        .print-controls {
            position: fixed;
            top: 15px;
            left: 15px;
            z-index: 1000;
            background-color: white;
            padding: 10px;
            border-radius: 6px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
            border: 1px solid #eee;
        }

        .page-number {
            text-align: center;
            margin-top: 15px;
            font-size: 10px;
            color: #999;
            position: fixed;
            bottom: 5px;
            left: 0;
            right: 0;
        }

        .page-number:after {
            content: counter(page);
        }
    </style>
</head>
<body>
    <div class="print-controls no-print">
        <button class="btn btn-primary" onclick="window.print()">
            <i class="fas fa-print me-1"></i> طباعة
        </button>
        <a href="{{ url_for('print_news') }}" class="btn btn-secondary ms-2">
            <i class="fas fa-arrow-right me-1"></i> رجوع
        </a>
    </div>

    <div class="header">
        <img src="{{ url_for('static', filename='img/ihchr.png') }}" alt="شعار المفوضية" class="logo">
        <div class="title">الرصد الإعلامي</div>
        <div class="subtitle">المفوضية العليا لحقوق الإنسان في العراق</div>
        <div class="department">قسم العلاقات والإعلام</div>
        <div class="department">وحدة الرصد الإعلامي</div>
        <div class="subtitle">{{ today_date }}</div>
    </div>

    <div class="date-bar">
        <div style="text-align: right;"><i class="fas fa-calendar-alt" style="margin-left: 8px;"></i>{{ today_date }}</div>
    </div>

    <div class="container">
        <div class="main-content" style="background-color: rgba(240, 248, 255, 0.7); padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);">
        {% if news_list %}
            {% for news in news_list %}
            <div class="news-item">
                <div class="news-title">{{ news.title }}</div>
                <div class="news-content">{{ news.content|nl2br|safe }}</div>
                <div class="news-meta">
                    <div class="meta-item meta-date">{{ news.date|format_date_arabic }}</div>
                    <div class="meta-item meta-source">
                        {% if news.source_url %}
                            <a href="{{ news.source_url }}" target="_blank" style="color: inherit; text-decoration: none;">
                                {{ news.source }}
                                <i class="fas fa-external-link-alt ms-1" style="font-size: 0.8em; color: #0056b3;"></i>
                            </a>
                        {% else %}
                            {{ news.source }}
                        {% endif %}
                    </div>
                    <div class="meta-item meta-location">{{ news.governorate.name }}</div>
                </div>
            </div>
            {% if not loop.last and loop.index % 4 == 0 %}
            <div class="page-break"></div>
            {% endif %}
            {% endfor %}
        {% else %}
            <div class="alert alert-info text-center p-5 my-5">
                <i class="fas fa-info-circle fa-3x mb-3"></i>
                <h3>لا توجد أخبار</h3>
                <p>لم يتم العثور على أخبار تطابق معايير البحث.</p>
            </div>
        {% endif %}
        </div>

        <div class="page-number">صفحة </div>
    </div>

    <div class="footer">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <img src="{{ url_for('static', filename='img/logo2.png') }}" alt="شعار نظام الرصد الإعلامي" style="width: 60px; height: auto; margin-bottom: 10px;">
                    <p>© {{ current_year }} نظام الرصد الإعلامي</p>
                    <p>وحدة الرصد الإعلامي - قسم العلاقات والإعلام</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // إضافة تاريخ الطباعة
        document.addEventListener('DOMContentLoaded', function() {
            const now = new Date();
            const formattedDate = now.toLocaleDateString('ar-IQ', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });

            const printInfo = document.createElement('div');
            printInfo.className = 'print-info';
            printInfo.style.cssText = 'position: fixed; bottom: 10px; left: 10px; font-size: 9px; color: #999; display: none;';
            printInfo.innerHTML = 'تمت الطباعة في: ' + formattedDate;
            document.body.appendChild(printInfo);

            window.onbeforeprint = function() {
                printInfo.style.display = 'block';
            };

            window.onafterprint = function() {
                printInfo.style.display = 'none';
            };
        });
    </script>
</body>
</html>
