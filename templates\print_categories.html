<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="robots" content="noindex, nofollow">
    <title>تقرير التصنيفات والحقول الديناميكية</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/bootstrap.rtl.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <style>
        @page {
            size: A4;
            margin: 1cm;
        }
        body {
            font-family: 'Tajawal', sans-serif;
            background-color: white;
            color: #333;
            padding: 0;
            margin: 0;
            line-height: 1.6;
        }
        .print-container {
            max-width: 1140px;
            margin: 0 auto;
            padding: 20px;
        }
        .print-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 3px solid #0d6efd;
            text-align: center;
        }
        .print-header-logo {
            max-height: 80px;
        }
        .print-header-content {
            text-align: center;
            flex-grow: 1;
            margin: 0 20px;
        }
        .print-title {
            font-size: 28px;
            font-weight: bold;
            margin: 5px 0;
            color: #0d6efd;
        }
        .print-subtitle {
            font-size: 16px;
            color: #6c757d;
            margin: 5px 0;
        }
        .print-date {
            font-size: 14px;
            color: #6c757d;
            margin: 5px 0;
        }

        .category-section {
            margin-bottom: 40px;
            page-break-inside: avoid;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .category-header {
            background: linear-gradient(135deg, #0d6efd 0%, #0056b3 100%);
            color: white;
            padding: 15px 20px;
            margin: 0;
        }
        .category-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin: 0;
            display: flex;
            justify-content: between;
            align-items: center;
        }
        .category-stats {
            font-size: 0.9rem;
            opacity: 0.9;
            margin-top: 5px;
        }
        .category-body {
            padding: 20px;
            background: white;
        }
        .category-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .fields-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        .fields-table th,
        .fields-table td {
            border: 1px solid #dee2e6;
            padding: 12px;
            text-align: right;
        }
        .fields-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }
        .fields-table tr:nth-child(even) {
            background: #f8f9fa;
        }
        .no-fields {
            text-align: center;
            color: #6c757d;
            font-style: italic;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        .footer {
            margin-top: 40px;
            text-align: center;
            color: #666;
            font-size: 12px;
            border-top: 1px solid #dee2e6;
            padding-top: 20px;
        }
        @media print {
            .no-print {
                display: none;
            }
            .category-section {
                page-break-inside: avoid;
            }
            body {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
        }
    </style>
</head>
<body>
    <div class="print-container">
        <!-- أزرار التحكم -->
        <div class="no-print mb-3 text-center">
            <button onclick="window.print()" class="btn btn-primary btn-lg me-2">
                <i class="fas fa-print"></i> طباعة التقرير
            </button>
            <a href="{{ url_for('manage_categories') }}" class="btn btn-secondary btn-lg">
                <i class="fas fa-arrow-right"></i> العودة للتصنيفات
            </a>
        </div>

        <!-- رأس التقرير -->
        <div class="print-header">
            <img src="{{ url_for('static', filename='img/ihchr.png') }}" alt="شعار المفوضية" class="print-header-logo">
            <div class="print-header-content">
                <div class="print-title">تقرير التصنيفات والحقول الديناميكية</div>
                <div class="print-subtitle">نظام الرصد الإعلامي</div>
                <div class="print-date">{{ today_date }}</div>
            </div>
            <img src="{{ url_for('static', filename='img/logo2.png') }}" alt="شعار النظام" class="print-header-logo">
        </div>

        <!-- التصنيفات والحقول الديناميكية -->
        <div class="categories-section">
            <h2 style="color: #0d6efd; margin-bottom: 20px;">
                <i class="fas fa-list me-2"></i>
                التصنيفات والحقول الديناميكية
            </h2>
            
            {% for category in categories_data %}
            <div class="category-section">
                <div class="category-header">
                    <div class="category-title">
                        <span>{{ category.name }}</span>
                        <span class="badge bg-light text-primary ms-auto">{{ category.fields_count }} حقل</span>
                    </div>
                    <div class="category-stats">
                        {{ category.fields_count }} حقل ديناميكي
                    </div>
                </div>
                
                <div class="category-body">
                    <!-- معلومات التصنيف -->
                    <div class="category-info">
                        <h6 class="mb-2">
                            <i class="fas fa-info-circle me-2"></i>
                            معلومات التصنيف
                        </h6>
                        <div class="row">
                            <div class="col-md-8">
                                <strong>الوصف:</strong> {{ category.description }}
                            </div>
                            <div class="col-md-4">
                                <strong>عدد الحقول:</strong> {{ category.fields_count }}
                            </div>
                        </div>
                    </div>
                    
                    <!-- الحقول الديناميكية -->
                    {% if category.fields %}
                    <h6 class="mb-3">
                        <i class="fas fa-cogs me-2"></i>
                        الحقول الديناميكية
                    </h6>
                    <table class="fields-table">
                        <thead>
                            <tr>
                                <th style="width: 25%;">اسم الحقل</th>
                                <th style="width: 15%;">نوع الحقل</th>
                                <th style="width: 10%;">مطلوب</th>
                                <th style="width: 50%;">الخيارات المتاحة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for field in category.fields %}
                            <tr>
                                <td><strong>{{ field.name }}</strong></td>
                                <td>{{ field.type }}</td>
                                <td>
                                    {% if field.required == 'نعم' %}
                                        <span class="text-danger">{{ field.required }}</span>
                                    {% else %}
                                        <span class="text-muted">{{ field.required }}</span>
                                    {% endif %}
                                </td>
                                <td>{{ field.options }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                    {% else %}
                    <div class="no-fields">
                        <i class="fas fa-info-circle me-2"></i>
                        لا توجد حقول ديناميكية لهذا التصنيف
                    </div>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- تذييل التقرير -->
        <div class="footer">
            <p><strong>تم إنشاء هذا التقرير بواسطة نظام الرصد الإعلامي</strong></p>
            <p>{{ today_date }}</p>
        </div>
    </div>

    <script>
        // طباعة تلقائية عند تحميل الصفحة (اختياري)
        // window.onload = function() {
        //     window.print();
        // };
    </script>
</body>
</html>
