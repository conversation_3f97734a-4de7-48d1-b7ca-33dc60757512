import { type RouteConfig, index, route } from "@react-router/dev/routes";

export default [
  index("routes/home.tsx"),

  // Authentication routes
  route("auth/signin", "routes/auth/signin.tsx"),
  route("auth/signup", "routes/auth/signup.tsx"),

  // News routes
  route("news", "routes/news/index.tsx"),
  route("news/add", "routes/news/add.tsx"),
  route("news/manage", "routes/news/manage.tsx"),
  route("news/:id", "routes/news/$id.tsx"),
  route("news/:id/edit", "routes/news/$id.edit.tsx"),

  // Main Categories routes (التصنيفات الرئيسية)
  route("main-categories", "routes/main-categories/index.tsx"),
  route("main-categories/add", "routes/main-categories/add.tsx"),
  route("main-categories/:id", "routes/main-categories/$id.tsx"),
  route("main-categories/:id/edit", "routes/main-categories/$id.edit.tsx"),

  // Violations routes (الانتهاكات)
  route("violations", "routes/violations/index.tsx"),
  route("violations/add", "routes/violations/add.tsx"),
  route("violations/:id", "routes/violations/$id.tsx"),
  route("violations/:id/edit", "routes/violations/$id.edit.tsx"),

  // Provinces routes
  route("provinces", "routes/provinces/index.tsx"),

  // Reports routes
  route("reports", "routes/reports/index.tsx"),
  route("reports/export", "routes/reports/export.tsx"),

  // Settings routes
  route("settings", "routes/settings/index.tsx"),

  // Dashboard routes (admin only)
  route("dashboard", "routes/dashboard/index.tsx"),
] satisfies RouteConfig;
