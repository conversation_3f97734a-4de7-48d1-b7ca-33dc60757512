import { useState, useEffect } from "react";
import { use<PERSON>ara<PERSON>, useNavigate, Link } from "react-router";
import { 
  ArrowRight, 
  Edit, 
  Trash2, 
  Calendar, 
  User, 
  MapPin, 
  Tag, 
  ExternalLink,
  AlertTriangle,
  Eye,
  Clock
} from "lucide-react";
import { Layout } from "~/components/Layout";
import type { NewsArticle, Classification, Violation, Source, Province } from "~/types";
import {
  MockNewsService as NewsService,
  MockClassificationService as ClassificationService,
  MockViolationService as ViolationService,
  MockSourceService as SourceService,
  MockProvinceService as ProvinceService
} from "~/lib/mockData";

function NewsDetailsPage() {
  const { id } = useParams();
  const navigate = useNavigate();
  const [news, setNews] = useState<NewsArticle | null>(null);
  const [classification, setClassification] = useState<Classification | null>(null);
  const [violation, setViolation] = useState<Violation | null>(null);
  const [source, setSource] = useState<Source | null>(null);
  const [province, setProvince] = useState<Province | null>(null);
  const [loading, setLoading] = useState(true);
  const [deleteConfirm, setDeleteConfirm] = useState(false);

  useEffect(() => {
    if (id) {
      loadNewsDetails();
    }
  }, [id]);

  const loadNewsDetails = async () => {
    try {
      setLoading(true);
      const newsData = await NewsService.getById(id!);
      
      if (!newsData) {
        navigate('/news/manage');
        return;
      }

      setNews(newsData);

      // Load related data
      const [classificationData, violationData, sourceData, provinceData] = await Promise.all([
        ClassificationService.getById(newsData.classificationId),
        ViolationService.getById(newsData.violationId),
        newsData.sourceId ? SourceService.getById(newsData.sourceId) : Promise.resolve(null),
        ProvinceService.getById(newsData.provinceId)
      ]);

      setClassification(classificationData);
      setViolation(violationData);
      setSource(sourceData);
      setProvince(provinceData);
    } catch (error) {
      console.error('Error loading news details:', error);
      navigate('/news/manage');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    try {
      await NewsService.delete(id!);
      navigate('/news/manage');
    } catch (error) {
      console.error('Error deleting news:', error);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <Layout>
        <div className="flex justify-center items-center min-h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحميل تفاصيل الخبر...</p>
          </div>
        </div>
      </Layout>
    );
  }

  if (!news) {
    return (
      <Layout>
        <div className="text-center py-12">
          <h3 className="text-lg font-medium text-gray-900 mb-2">الخبر غير موجود</h3>
          <p className="text-gray-600 mb-6">لم يتم العثور على الخبر المطلوب</p>
          <Link
            to="/news/manage"
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg inline-flex items-center gap-2"
          >
            <ArrowRight className="h-5 w-5" />
            العودة إلى قائمة الأخبار
          </Link>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <button
              onClick={() => navigate('/news/manage')}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <ArrowRight className="h-6 w-6" />
            </button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">تفاصيل الخبر</h1>
              <p className="text-gray-600 mt-2">عرض تفاصيل الخبر والمعلومات المرتبطة به</p>
            </div>
          </div>
          
          <div className="flex items-center gap-3">
            <Link
              to={`/news/${id}/edit`}
              className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
            >
              <Edit className="h-4 w-4" />
              تعديل
            </Link>
            <button
              onClick={() => setDeleteConfirm(true)}
              className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
            >
              <Trash2 className="h-4 w-4" />
              حذف
            </button>
          </div>
        </div>

        {/* News Content */}
        <div className="bg-white rounded-lg shadow overflow-hidden">
          {/* Image */}
          {news.imageUrl && (
            <div className="aspect-video bg-gray-200">
              <img
                src={news.imageUrl}
                alt={news.title}
                className="w-full h-full object-cover"
              />
            </div>
          )}

          <div className="p-8">
            {/* Title */}
            <div className="mb-6">
              <h1 className="text-3xl font-bold text-gray-900 leading-tight">
                {news.title}
              </h1>
            </div>

            {/* Meta Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8 p-6 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-3">
                <Calendar className="h-5 w-5 text-gray-500" />
                <div>
                  <p className="text-sm font-medium text-gray-700">التاريخ</p>
                  <p className="text-gray-900">{formatDate(news.date)}</p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <MapPin className="h-5 w-5 text-gray-500" />
                <div>
                  <p className="text-sm font-medium text-gray-700">المحافظة</p>
                  <p className="text-gray-900">{province?.name || 'غير محدد'}</p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <Tag className="h-5 w-5 text-gray-500" />
                <div>
                  <p className="text-sm font-medium text-gray-700">التصنيف</p>
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                    {classification?.name || 'غير محدد'}
                  </span>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <AlertTriangle className="h-5 w-5 text-gray-500" />
                <div>
                  <p className="text-sm font-medium text-gray-700">الانتهاك</p>
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-sm font-medium bg-red-100 text-red-800">
                    {violation?.name || 'غير محدد'}
                  </span>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <ExternalLink className="h-5 w-5 text-gray-500" />
                <div>
                  <p className="text-sm font-medium text-gray-700">المصدر</p>
                  <p className="text-gray-900">{source?.name || 'غير محدد'}</p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <ExternalLink className="h-5 w-5 text-gray-500" />
                <div>
                  <p className="text-sm font-medium text-gray-700">رابط المصدر</p>
                  <a
                    href={news.sourceUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-800 underline"
                  >
                    عرض المصدر
                  </a>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <Clock className="h-5 w-5 text-gray-500" />
                <div>
                  <p className="text-sm font-medium text-gray-700">تاريخ الإنشاء</p>
                  <p className="text-gray-900">{formatDate(news.createdAt)}</p>
                </div>
              </div>
            </div>

            {/* Content */}
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">محتوى الخبر</h2>
              <div className="prose prose-lg max-w-none text-gray-700 leading-relaxed">
                {news.content.split('\n').map((paragraph, index) => (
                  <p key={index} className="mb-4">
                    {paragraph}
                  </p>
                ))}
              </div>
            </div>

            {/* Related Rights Information */}
            {violation && (
              <div className="mb-8">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">الحق المنتهك</h3>
                <div className="bg-red-50 rounded-lg p-6">
                  <p className="text-red-800 font-medium">{violation.relatedRight}</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Delete Confirmation Modal */}
        {deleteConfirm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
              <div className="flex items-center mb-4">
                <div className="p-3 rounded-full bg-red-100 ml-3">
                  <AlertTriangle className="h-6 w-6 text-red-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900">تأكيد الحذف</h3>
              </div>
              
              <p className="text-gray-600 mb-6">
                هل أنت متأكد من حذف هذا الخبر؟ هذا الإجراء لا يمكن التراجع عنه.
              </p>
              
              <div className="flex gap-3 justify-end">
                <button
                  onClick={() => setDeleteConfirm(false)}
                  className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
                >
                  إلغاء
                </button>
                <button
                  onClick={handleDelete}
                  className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md transition-colors"
                >
                  حذف
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
}

export default NewsDetailsPage;
