{"name": "media-monitoring-system", "private": true, "type": "module", "scripts": {"build": "react-router build", "dev": "react-router dev", "start": "react-router-serve ./build/server/index.js", "typecheck": "react-router typegen && tsc"}, "dependencies": {"@react-router/node": "^7.7.1", "@react-router/serve": "^7.7.1", "firebase": "^11.1.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router": "^7.7.1", "react-hook-form": "^7.54.2", "date-fns": "^4.1.0", "lucide-react": "^0.468.0", "recharts": "^2.13.3", "jspdf": "^2.5.2", "xlsx": "^0.18.5", "isbot": "^5.1.27"}, "devDependencies": {"@react-router/dev": "^7.7.1", "@tailwindcss/vite": "^4.1.4", "@types/node": "^20", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "tailwindcss": "^4.1.4", "typescript": "^5.8.3", "vite": "^6.3.3", "vite-tsconfig-paths": "^5.1.4"}}