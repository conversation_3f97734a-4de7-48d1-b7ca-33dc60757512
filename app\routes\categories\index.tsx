import { useState, useEffect } from "react";
import { Link } from "react-router";
import { 
  Plus, 
  Edit, 
  Trash2, 
  Setting<PERSON>, 
  FileText, 
  AlertTriangle,
  Users,
  Baby,
  Shield
} from "lucide-react";
import { Layout } from "~/components/Layout";
import type { Classification, LegalFile } from "~/types";
import { 
  MockClassificationService as ClassificationService,
  MockLegalFileService as LegalFileService 
} from "~/lib/mockData";

// Icon mapping
const iconMap = {
  AlertTriangle,
  Users,
  Baby,
  Shield,
  FileText,
  Settings
};

function ClassificationsPage() {
  const [classifications, setClassifications] = useState<Classification[]>([]);
  const [legalFiles, setLegalFiles] = useState<LegalFile[]>([]);
  const [loading, setLoading] = useState(true);
  const [deleteConfirm, setDeleteConfirm] = useState<string | null>(null);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [classificationsData, legalFilesData] = await Promise.all([
        ClassificationService.getAll(),
        LegalFileService.getAll()
      ]);
      setClassifications(classificationsData);
      setLegalFiles(legalFilesData);
    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    try {
      await ClassificationService.delete(id);
      setClassifications(prev => prev.filter(c => c.id !== id));
      setDeleteConfirm(null);
    } catch (error) {
      console.error('Error deleting classification:', error);
    }
  };

  const getLegalFileName = (legalFileId: string) => {
    return legalFiles.find(f => f.id === legalFileId)?.name || 'غير محدد';
  };

  const getIcon = (iconName: string) => {
    const IconComponent = iconMap[iconName as keyof typeof iconMap] || FileText;
    return IconComponent;
  };

  if (loading) {
    return (
      <Layout>
        <div className="flex justify-center items-center min-h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحميل التصنيفات...</p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">إدارة التصنيفات</h1>
            <p className="text-gray-600 mt-2">
              إدارة تصنيفات الأخبار والحقول الديناميكية المرتبطة بها
            </p>
          </div>
          <Link
            to="/categories/add"
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg flex items-center gap-2 transition-colors"
          >
            <Plus className="h-5 w-5" />
            إضافة تصنيف جديد
          </Link>
        </div>

        {/* Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-blue-100">
                <Settings className="h-6 w-6 text-blue-600" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">إجمالي التصنيفات</p>
                <p className="text-2xl font-bold text-gray-900">{classifications.length}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-green-100">
                <Shield className="h-6 w-6 text-green-600" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">التصنيفات النشطة</p>
                <p className="text-2xl font-bold text-gray-900">
                  {classifications.length}
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-purple-100">
                <FileText className="h-6 w-6 text-purple-600" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">الملفات الحقوقية</p>
                <p className="text-2xl font-bold text-gray-900">{legalFiles.length}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Classifications Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {classifications.map((classification) => {
            const IconComponent = getIcon('FileText');
            return (
              <div
                key={classification.id}
                className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow border-r-4 border-r-blue-500"
              >
                <div className="p-6">
                  {/* Header */}
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center">
                      <div
                        className="p-3 rounded-full ml-3 bg-blue-100"
                      >
                        <IconComponent
                          className="h-6 w-6 text-blue-600"
                        />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">
                          {classification.name}
                        </h3>
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          نشط
                        </span>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <Link
                        to={`/categories/${classification.id}/edit`}
                        className="p-2 text-gray-400 hover:text-blue-600 transition-colors"
                        title="تعديل"
                      >
                        <Edit className="h-4 w-4" />
                      </Link>
                      <button
                        onClick={() => setDeleteConfirm(classification.id)}
                        className="p-2 text-gray-400 hover:text-red-600 transition-colors"
                        title="حذف"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </div>

                  {/* Description */}
                  <p className="text-gray-600 text-sm mb-4 leading-relaxed">
                    {classification.description}
                  </p>

                  {/* Legal File */}
                  <div className="bg-gray-50 rounded-md p-3 mb-4">
                    <p className="text-xs font-medium text-gray-500 mb-1">الملف الحقوقي المرتبط:</p>
                    <p className="text-sm text-gray-700">
                      {getLegalFileName(classification.legalFileId)}
                    </p>
                  </div>

                  {/* Actions */}
                  <div className="flex gap-2">
                    <Link
                      to={`/categories/${classification.id}/edit`}
                      className="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-md text-sm font-medium text-center transition-colors"
                    >
                      تعديل التصنيف
                    </Link>
                    <Link
                      to={`/categories/${classification.id}`}
                      className="flex-1 bg-blue-100 hover:bg-blue-200 text-blue-700 px-4 py-2 rounded-md text-sm font-medium text-center transition-colors"
                    >
                      عرض التفاصيل
                    </Link>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Empty State */}
        {classifications.length === 0 && (
          <div className="text-center py-12">
            <Settings className="h-16 w-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد تصنيفات</h3>
            <p className="text-gray-600 mb-6">ابدأ بإنشاء تصنيف جديد لتنظيم الأخبار</p>
            <Link
              to="/categories/add"
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg inline-flex items-center gap-2"
            >
              <Plus className="h-5 w-5" />
              إضافة تصنيف جديد
            </Link>
          </div>
        )}

        {/* Delete Confirmation Modal */}
        {deleteConfirm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
              <div className="flex items-center mb-4">
                <div className="p-3 rounded-full bg-red-100 ml-3">
                  <AlertTriangle className="h-6 w-6 text-red-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900">تأكيد الحذف</h3>
              </div>
              
              <p className="text-gray-600 mb-6">
                هل أنت متأكد من حذف هذا التصنيف؟ سيتم حذف جميع الحقول المرتبطة به أيضاً. 
                هذا الإجراء لا يمكن التراجع عنه.
              </p>
              
              <div className="flex gap-3 justify-end">
                <button
                  onClick={() => setDeleteConfirm(null)}
                  className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
                >
                  إلغاء
                </button>
                <button
                  onClick={() => handleDelete(deleteConfirm)}
                  className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md transition-colors"
                >
                  حذف
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
}

export default ClassificationsPage;
