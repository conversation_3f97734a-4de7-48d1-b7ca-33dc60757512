// User Types (المستخدمين)
export interface User {
  id: string;
  username: string;
  email: string;
  password: string; // مشفرة
  role: 'admin' | 'editor' | 'viewer';
  createdAt: Date;
  lastLoginAt?: Date;
}

// Province Types (المحافظات)
export interface Province {
  id: string;
  name: string;
}

// Source Types (المصادر)
export interface Source {
  id: string;
  name: string;
  description?: string;
  contactInfo?: string;
  additionalData?: Record<string, any>;
}

// Main Categories (التصنيفات الرئيسية - الحقوق الأساسية)
export interface MainCategory {
  id: string;
  name: string;
  description: string;
  isActive: boolean;
}

// Violations (الانتهاكات الفرعية)
export interface Violation {
  id: string;
  mainCategoryId: string;
  name: string;
  description: string;
  relatedRight: string;
  isActive: boolean;
}

// Dynamic Field Types (أنواع الحقول الديناميكية)
export type FieldType = 'text' | 'textarea' | 'number' | 'select';

// Dynamic Fields (الحقول الديناميكية)
export interface DynamicField {
  id: string;
  violationId: string;
  name: string;
  label: string;
  type: FieldType;
  isRequired: boolean;
  placeholder?: string;
  options?: string[]; // للقوائم المنسدلة
  order: number;
}

// Field Values (قيم الحقول الديناميكية)
export interface FieldValue {
  id: string;
  newsId: string;
  fieldId: string;
  value: string; // قيمة موحدة (نص، رقم، أو خيار محدد)
}

// News Article Types (الأخبار)
export interface NewsArticle {
  id: string;
  title: string; // العنوان
  content: string; // التفاصيل
  provinceId: string; // معرف_المحافظة
  date: string; // تاريخ_الخبر
  sourceUrl: string; // رابط_المصدر
  sourceId?: string; // معرف_المصدر (اختياري)
  mainCategoryId: string; // معرف_التصنيف_الرئيسي
  violationId: string; // معرف_الانتهاك
  createdBy: string; // معرف_المنشئ

  // Relations for display (للعرض فقط)
  province?: Province;
  source?: Source;
  mainCategory?: MainCategory;
  violation?: Violation;
  creator?: User;
}

// Search and Filter Types
export interface SearchFilters {
  mainCategoryId?: string;
  violationId?: string;
  sourceId?: string;
  provinceId?: string;
  dateFrom?: string;
  dateTo?: string;
  searchText?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

export interface SearchResult<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Form Types (أنواع النماذج)
export interface NewsForm {
  title: string;
  content: string;
  provinceId: string;
  date: string;
  sourceUrl: string;
  sourceId?: string;
  mainCategoryId: string;
  violationId: string;
  dynamicFields: Record<string, any>;
}

export interface ViolationForm {
  name: string;
  description: string;
  relatedRight: string;
  mainCategoryId: string;
}

export interface MainCategoryForm {
  name: string;
  description: string;
}

// Statistics Types (أنواع الإحصائيات)
export interface DashboardStats {
  totalNews: number;
  newsByProvince: { provinceId: string; provinceName: string; count: number }[];
  newsByMainCategory: { mainCategoryId: string; mainCategoryName: string; count: number }[];
  newsByViolation: { violationId: string; violationName: string; count: number }[];
  newsByDate: { date: string; count: number }[];
}

// Search and Filter Types
export interface SearchFilters {
  searchTerm?: string;
  mainCategoryId?: string;
  violationId?: string;
  provinceId?: string;
  sourceId?: string;
  startDate?: Date;
  endDate?: Date;
  status?: NewsArticle['status'];
  tags?: string[];
  createdBy?: string;
  sortBy?: 'createdAt' | 'publishedDate' | 'title' | 'viewCount';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

export interface SearchResult<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Form Types
export interface NewsArticleForm {
  title: string;
  content: string;
  summary?: string;
  provinceId: string;
  sourceId: string;
  sourceUrl?: string;
  categoryId: string;
  publishedDate: Date;
  status: NewsArticle['status'];
  tags: string[];
  isPublic: boolean;
  dynamicData: Record<string, any>;
}

export interface CategoryForm {
  name: string;
  description?: string;
  color?: string;
  icon?: string;
  dynamicFields: Omit<DynamicField, 'id'>[];
}

export interface DynamicFieldForm {
  name: string;
  type: DynamicField['type'];
  required: boolean;
  options?: string[];
  placeholder?: string;
  validation?: DynamicField['validation'];
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> extends ApiResponse<SearchResult<T>> {}

// Dashboard Types
export interface DashboardStats {
  totalArticles: number;
  totalCategories: number;
  totalSources: number;
  totalProvinces: number;
  articlesThisMonth: number;
  articlesThisWeek: number;
  articlesToday: number;
  recentArticles: NewsArticle[];
  topCategories: { categoryId: string; categoryName: string; count: number }[];
  topProvinces: { provinceId: string; provinceName: string; count: number }[];
}

// Notification Types
export interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  read: boolean;
  createdAt: Date;
  userId: string;
  actionUrl?: string;
}

// Settings Types
export interface SystemSettings {
  siteName: string;
  siteDescription: string;
  contactEmail: string;
  maxFileSize: number; // in MB
  allowedFileTypes: string[];
  articlesPerPage: number;
  enablePublicAccess: boolean;
  enableNotifications: boolean;
  defaultLanguage: 'ar' | 'en';
  timezone: string;
}

// Export all types
export type {
  User,
  Province,
  Source,
  DynamicField,
  Category,
  NewsArticle,
  Attachment,
  ReportFilter,
  ReportData,
  SearchFilters,
  SearchResult,
  NewsArticleForm,
  CategoryForm,
  DynamicFieldForm,
  ApiResponse,
  PaginatedResponse,
  DashboardStats,
  Notification,
  SystemSettings,
};
