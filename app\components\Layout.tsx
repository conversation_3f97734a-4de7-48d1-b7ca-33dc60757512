import { Link, useLocation } from "react-router";
import { useState } from "react";
import {
  Home,
  Plus,
  FileText,
  Tags,
  AlertTriangle,
  MapPin,
  BarChart3,
  Settings,
  Menu, 
  X,
  LogOut,
  User
} from "lucide-react";

interface LayoutProps {
  children: React.ReactNode;
  user?: {
    id: string;
    displayName: string;
    role: 'admin' | 'user';
  } | null;
  onSignOut?: () => void;
}

export function Layout({ children, user, onSignOut }: LayoutProps) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const location = useLocation();

  const navigation = [
    { name: 'الرئيسية', href: '/', icon: Home },
    { name: 'إضافة خبر', href: '/news/add', icon: Plus },
    { name: 'عرض الأخبار', href: '/news', icon: FileText },
    { name: 'إدارة الأخبار', href: '/news/manage', icon: FileText },
    { name: 'التصنيفات الرئيسية', href: '/main-categories', icon: Tags },
    { name: 'الانتهاكات', href: '/violations', icon: AlertTriangle },
    { name: 'إدارة المحافظات', href: '/provinces', icon: MapPin, adminOnly: true },
    { name: 'الإحصائيات', href: '/reports', icon: BarChart3 },
    { name: 'الإعدادات', href: '/settings', icon: Settings },
  ];

  const filteredNavigation = navigation.filter(item => 
    !item.adminOnly || (user?.role === 'admin')
  );

  const isActive = (href: string) => {
    if (href === '/') {
      return location.pathname === '/';
    }
    return location.pathname.startsWith(href);
  };

  return (
    <div className="min-h-screen bg-gray-50 rtl">
      {/* Navigation */}
      <nav className="bg-blue-600 shadow-lg">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              {/* Logo */}
              <Link to="/" className="flex items-center space-x-2 space-x-reverse">
                <img 
                  src="/logo2.png" 
                  alt="شعار النظام" 
                  className="h-8 w-auto small-logo floating-logo"
                />
                <span className="text-white font-bold text-lg">نظام الرصد الإعلامي</span>
              </Link>
            </div>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-4 space-x-reverse">
              {filteredNavigation.map((item) => {
                const Icon = item.icon;
                return (
                  <Link
                    key={item.name}
                    to={item.href}
                    className={`flex items-center space-x-1 space-x-reverse px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                      isActive(item.href)
                        ? 'bg-blue-700 text-white'
                        : 'text-blue-100 hover:bg-blue-500 hover:text-white'
                    }`}
                  >
                    <Icon className="h-4 w-4" />
                    <span>{item.name}</span>
                  </Link>
                );
              })}
            </div>

            {/* User Menu */}
            <div className="hidden md:flex items-center space-x-4 space-x-reverse">
              {user ? (
                <div className="flex items-center space-x-4 space-x-reverse">
                  <div className="flex items-center space-x-2 space-x-reverse text-white">
                    <User className="h-4 w-4" />
                    <span className="text-sm">{user.displayName}</span>
                    <span className="text-xs bg-blue-500 px-2 py-1 rounded">
                      {user.role === 'admin' ? 'مدير' : 'مستخدم'}
                    </span>
                  </div>
                  <button
                    onClick={onSignOut}
                    className="flex items-center space-x-1 space-x-reverse text-blue-100 hover:text-white transition-colors"
                  >
                    <LogOut className="h-4 w-4" />
                    <span className="text-sm">خروج</span>
                  </button>
                </div>
              ) : (
                <Link
                  to="/auth/signin"
                  className="text-blue-100 hover:text-white transition-colors"
                >
                  تسجيل الدخول
                </Link>
              )}
            </div>

            {/* Mobile menu button */}
            <div className="md:hidden flex items-center">
              <button
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                className="text-blue-100 hover:text-white p-2"
              >
                {isMobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
              </button>
            </div>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMobileMenuOpen && (
          <div className="md:hidden bg-blue-700">
            <div className="px-2 pt-2 pb-3 space-y-1">
              {filteredNavigation.map((item) => {
                const Icon = item.icon;
                return (
                  <Link
                    key={item.name}
                    to={item.href}
                    onClick={() => setIsMobileMenuOpen(false)}
                    className={`flex items-center space-x-2 space-x-reverse px-3 py-2 rounded-md text-base font-medium transition-colors ${
                      isActive(item.href)
                        ? 'bg-blue-800 text-white'
                        : 'text-blue-100 hover:bg-blue-600 hover:text-white'
                    }`}
                  >
                    <Icon className="h-5 w-5" />
                    <span>{item.name}</span>
                  </Link>
                );
              })}
              
              {user && (
                <div className="border-t border-blue-600 pt-4 mt-4">
                  <div className="px-3 py-2 text-blue-100 text-sm">
                    {user.displayName} ({user.role === 'admin' ? 'مدير' : 'مستخدم'})
                  </div>
                  <button
                    onClick={() => {
                      onSignOut?.();
                      setIsMobileMenuOpen(false);
                    }}
                    className="flex items-center space-x-2 space-x-reverse w-full px-3 py-2 text-blue-100 hover:text-white hover:bg-blue-600 rounded-md transition-colors"
                  >
                    <LogOut className="h-5 w-5" />
                    <span>خروج</span>
                  </button>
                </div>
              )}
            </div>
          </div>
        )}
      </nav>

      {/* Main Content */}
      <main className="flex-1">
        {children}
      </main>

      {/* Footer */}
      <footer className="bg-gray-100 border-t">
        <div className="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8">
          <div className="text-center text-gray-600 text-sm">
            © 2025 نظام الرصد الإعلامي لحقوق الإنسان - جميع الحقوق محفوظة
          </div>
        </div>
      </footer>
    </div>
  );
}
